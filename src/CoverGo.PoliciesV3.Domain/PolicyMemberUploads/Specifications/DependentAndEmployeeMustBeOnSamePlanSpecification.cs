using CoverGo.PoliciesV3.Domain.Common.Extensions;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications;

/// <summary>
/// Business Rules:
/// - Each dependent must have the same plan ID as their primary member (employee)
/// - Only applies to dependent members (memberType = "dependent")
/// - Primary member plan ID is cached for efficiency during upload validation
/// - Validation is performed across the entire upload file
/// - Feature flag controlled: "UseTheSamePlanForEmployeeAndDependents"
/// </summary>
public class DependentAndEmployeeMustBeOnSamePlanSpecification(
    ILogger<DependentAndEmployeeMustBeOnSamePlanSpecification> logger)
    : ISpecification<DependentPlanValidationContext>
{
    public string BusinessRuleName => "Dependent And Employee Must Be On Same Plan";
    public string Description => "Validates that dependent members have the same plan ID as their primary member (employee) within the upload file when feature flags enable this validation";

    public async Task<Result> IsSatisfiedBy(DependentPlanValidationContext context, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(context);

        try
        {
            // Use Task.CompletedTask for in-memory operations to maintain async pattern consistency
            await Task.CompletedTask;

            if (!context.ShouldExecuteValidation())
            {
                logger.LogDebug("Dependent plan validation skipped due to feature flag configuration");
                return Result.Success();
            }

            Dictionary<int, int> primaryMemberIndex = BuildPrimaryMemberIndex(context.MembersFields);
            Dictionary<int, List<ValidationError>> memberErrors = ValidateDependentPlanMatching(context.MembersFields, primaryMemberIndex);

            if (memberErrors.Count > 0)
            {
                var allErrors = memberErrors.Values.Where(errors => errors != null).SelectMany(errors => errors).ToList();
                logger.LogWarning("Dependent plan validation failed. Found {ErrorCount} plan mismatch errors across {MemberCount} dependents",
                    allErrors.Count, memberErrors.Count);
                return Result.Failure(allErrors);
            }

            logger.LogDebug("Dependent plan validation passed - all dependents have matching plans with their employees");
            return Result.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during dependent plan validation");
            throw;
        }
    }

    /// <summary>
    /// Validates the context and returns errors indexed by row number for upload-wide validation scenarios.
    /// </summary>
    /// <param name="context">The dependent plan validation context</param>
    /// <returns>Dictionary mapping row indices to validation errors</returns>
    public virtual Dictionary<int, List<ValidationError>> ValidateWithRowIndexedErrors(DependentPlanValidationContext context)
    {
        ArgumentNullException.ThrowIfNull(context);

        try
        {
            if (!context.ShouldExecuteValidation())
            {
                logger.LogDebug("Dependent plan validation skipped due to feature flag configuration");
                return [];
            }

            Dictionary<int, int> primaryMemberIndex = BuildPrimaryMemberIndex(context.MembersFields);
            return ValidateDependentPlanMatching(context.MembersFields, primaryMemberIndex);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during dependent plan validation");
            throw;
        }
    }

    /// <summary>
    /// Pre-computes primary member relationships in O(n) time to avoid O(n²) complexity.
    /// Maps each member index to their primary member index (or -1 if no primary member).
    /// </summary>
    private static Dictionary<int, int> BuildPrimaryMemberIndex(MembersUploadFields membersFields)
    {
        var primaryMemberIndex = new Dictionary<int, int>();
        int lastPrimaryMemberIndex = -1;

        // Single O(n) pass to build the index
        for (int i = 0; i < membersFields.Count; i++)
        {
            MemberUploadFields member = membersFields[i];

            if (!member.IsDependent())
            {
                // This is a primary member - update the last known primary
                lastPrimaryMemberIndex = i;
                primaryMemberIndex[i] = -1; // Primary members don't have a primary member
            }
            else
            {
                // This is a dependent - assign the last known primary member
                primaryMemberIndex[i] = lastPrimaryMemberIndex;
            }
        }

        return primaryMemberIndex;
    }

    private static Dictionary<int, List<ValidationError>> ValidateDependentPlanMatching(
        MembersUploadFields membersFields,
        Dictionary<int, int> primaryMemberIndex)
    {
        var validationErrors = new Dictionary<int, List<ValidationError>>();

        for (int i = 0; i < membersFields.Count; i++)
        {
            MemberUploadFields member = membersFields[i];
            if (!member.IsDependent())
                continue;

            // Use O(1) index lookup instead of O(k) backward search
            if (!primaryMemberIndex.TryGetValue(i, out int primaryIndex) || primaryIndex == -1)
                continue;

            MemberUploadFields primaryMember = membersFields[primaryIndex];
            string? primaryMemberPlanId = primaryMember.Value.TryGetValueOrDefault(PolicyMemberUploadWellKnowFields.PlanIdField);
            string? dependentMemberPlanId = member.Value.TryGetValueOrDefault(PolicyMemberUploadWellKnowFields.PlanIdField);

            if (dependentMemberPlanId == primaryMemberPlanId)
                continue;

            ValidationError error = Errors.DependentPlanMismatch(PolicyMemberUploadWellKnowFields.PlanIdField, "Plan ID");

            if (!validationErrors.TryGetValue(i, out List<ValidationError>? errorList))
            {
                errorList = [];
                validationErrors[i] = errorList;
            }

            errorList.Add(error);
        }

        return validationErrors;
    }
}
