using CoverGo.PoliciesV3.Domain.Common.Constants;
using CoverGo.PoliciesV3.Domain.Common.Extensions;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Domain.PolicyMemberUploads.Specifications;

/// <summary>
/// Business Rules:
/// - Member IDs must be unique within the upload file for existing members (with memberIds)
/// - Empty or null member ID values are not validated for uniqueness
/// - Only existing members (with memberIds) are validated for member ID uniqueness
/// - New members (without memberIds) are excluded from member ID uniqueness validation
/// - This is the inverse of other upload uniqueness validations which target new members
/// </summary>
public class UploadMustHaveUniqueMemberIdsSpecification(
    ILogger<UploadMustHaveUniqueMemberIdsSpecification> logger)
    : ISpecification<UploadUniquenessValidationContext>
{
    public string BusinessRuleName => "Upload Must Have Unique Member IDs";
    public string Description => "Validates that member IDs are unique within the upload file to prevent duplicate member ID entries for existing members";

    public async Task<Result> IsSatisfiedBy(UploadUniquenessValidationContext context, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(context);

        try
        {
            await Task.CompletedTask;

            Dictionary<string, List<int>> memberIdIndex = BuildMemberIdIndex(context.MembersFields);
            Dictionary<int, List<ValidationError>> memberErrors = ValidateMemberIdUniqueness(memberIdIndex);

            if (memberErrors.Count > 0)
            {
                var allErrors = memberErrors.Values.Where(errors => errors != null).SelectMany(errors => errors).ToList();
                logger.LogWarning("Upload member ID uniqueness validation failed. Found {ErrorCount} duplicate member ID errors across {MemberCount} members",
                    allErrors.Count, memberErrors.Count);
                return Result.Failure(allErrors);
            }

            logger.LogDebug("Upload member ID uniqueness validation passed - no duplicate member IDs found");
            return Result.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during upload member ID uniqueness validation");
            throw;
        }
    }

    private static Dictionary<string, List<int>> BuildMemberIdIndex(MembersUploadFields membersFields)
    {
        var memberIdIndex = new Dictionary<string, List<int>>(StringComparer.OrdinalIgnoreCase);

        for (int i = 0; i < membersFields.Count; i++)
        {
            MemberUploadFields memberFields = membersFields[i];

            string? memberId = memberFields.Value.TryGetValueOrDefault(PolicyMemberUploadWellKnowFields.MemberIdField);
            if (string.IsNullOrWhiteSpace(memberId))
                continue; // Skip new members (without memberIds)

            if (!memberIdIndex.TryGetValue(memberId, out List<int>? indexList))
            {
                indexList = [];
                memberIdIndex[memberId] = indexList;
            }
            indexList.Add(i);
        }

        return memberIdIndex;
    }

    private static Dictionary<int, List<ValidationError>> ValidateMemberIdUniqueness(
        Dictionary<string, List<int>> memberIdIndex)
    {
        var validationErrors = new Dictionary<int, List<ValidationError>>();

        foreach (List<int> duplicateIndexes in memberIdIndex.Values.Where(indexes => indexes.Count > 1))
        {
            foreach (int memberIndex in duplicateIndexes)
            {
                ValidationError error = Errors.UniqueViolation(ValidationConstants.PropertyPaths.MemberId, ValidationConstants.Labels.MemberId, ValidationConstants.Scopes.Upload);

                if (!validationErrors.TryGetValue(memberIndex, out List<ValidationError>? errors))
                {
                    errors = [];
                    validationErrors[memberIndex] = errors;
                }
                errors.Add(error);
            }
        }

        return validationErrors;
    }

    /// <summary>
    /// Validates member ID uniqueness and returns row-indexed errors for integration with validation orchestration.
    /// </summary>
    public virtual Dictionary<int, List<ValidationError>> ValidateWithRowIndexedErrors(UploadUniquenessValidationContext context)
    {
        ArgumentNullException.ThrowIfNull(context);

        try
        {
            Dictionary<string, List<int>> memberIdIndex = BuildMemberIdIndex(context.MembersFields);
            return ValidateMemberIdUniqueness(memberIdIndex);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during upload member ID uniqueness validation with row-indexed errors");
            throw;
        }
    }
}
