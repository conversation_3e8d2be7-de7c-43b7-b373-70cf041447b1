using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Exceptions;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;

public class MemberIdMustFollowBusinessRulesSpecification(
    ILogger<MemberIdMustFollowBusinessRulesSpecification> logger)
    : ISpecification<MemberIdValidationContext>
{
    public string BusinessRuleName => "Member ID Must Follow Business Rules";
    public string Description => "Validates that member IDs comply with business rules including member existence, policy membership, and contract holder constraints.";

    public virtual async Task<Result> IsSatisfiedBy(MemberIdValidationContext context, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(context);

        await Task.CompletedTask;

        string? memberId = context.GetMemberId();
        if (string.IsNullOrWhiteSpace(memberId))
        {
            logger.LogInformation("MemberId is null or empty. Skipping validation.");
            return Result.Success();
        }

        logger.LogInformation("EnsureCanSetMemberId: PolicyId {PolicyId}, MemberId {MemberId}", context.Policy.Id, memberId);

        try
        {
            EnsureIndividualWithMemberIdExists(context, memberId);
            EnsureIndividualWithMemberIdNotTaken(context, memberId);

            if (!context.AllowMembersFromOtherContractHolders)
            {
                logger.LogInformation("Feature 'AllowMembersFromOtherContractHolders' is disabled. Checking contract holder constraint for MemberId {MemberId}.", memberId);
                EnsureMemberNotInAnotherContractHolder(context, memberId);
            }
            else
            {
                logger.LogInformation("Feature 'AllowMembersFromOtherContractHolders' is enabled. Skipping contract holder constraint check for MemberId {MemberId}.", memberId);
            }

            return Result.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during EnsureCanSetMemberId for PolicyId {PolicyId}, MemberId {MemberId}", context.Policy.Id, memberId);
            List<ValidationError> errors = CreateMemberIdValidationError(ex, memberId);
            return Result.Failure(errors);
        }
    }

    #region Private Member ID Validation Helpers

    /// <summary>
    /// Ensures an individual with the given member ID exists (uses pre-resolved data from context)
    /// </summary>
    private void EnsureIndividualWithMemberIdExists(MemberIdValidationContext context, string memberId)
    {
        if (!context.IndividualExists)
        {
            logger.LogWarning("Individual not found for MemberId {MemberId}", memberId);
            throw new MemberNotFoundException(memberId);
        }
    }

    /// <summary>
    /// Ensures the member ID is not already taken by another policy member in the same policy
    /// </summary>
    private void EnsureIndividualWithMemberIdNotTaken(MemberIdValidationContext context, string memberId)
    {
        PolicyMember? policyMemberWithMemberId = context.ExistingPolicyMember;

        if (policyMemberWithMemberId != null)
        {
            logger.LogWarning("MemberId {MemberId} already exists in Policy {PolicyId} with PolicyMemberId {ExistingPolicyMemberId}",
                memberId, context.Policy.Id, policyMemberWithMemberId.Id);

            throw new PolicyMemberExistsException(policyMemberWithMemberId.Id, memberId);
        }
    }

    /// <summary>
    /// Ensures the member is not associated with policies from other contract holders
    /// </summary>
    private void EnsureMemberNotInAnotherContractHolder(MemberIdValidationContext context, string memberId)
    {
        if (context.MemberValidationStates.Count == 0)
            return;

        var contractHolderPolicySet = context.ContractHolderPolicyIds.ToHashSet();
        var violatingPolicyIds = new List<string>();

        foreach (PolicyMember policyMember in context.MemberValidationStates)
        {
            string policyIdString = policyMember.PolicyId.Value.ToString();
            if (!contractHolderPolicySet.Contains(policyIdString))
            {
                violatingPolicyIds.Add(policyIdString);
            }
        }

        if (violatingPolicyIds.Count > 0)
        {
            logger.LogWarning("MemberId {MemberId} found in {ViolationCount} policies not part of current contract holder: {PolicyIds}",
                memberId, violatingPolicyIds.Count, string.Join(", ", violatingPolicyIds));
            throw new MemberNotInContractHolderException(memberId, violatingPolicyIds[0]);
        }
    }

    /// <summary>
    /// Creates appropriate member ID validation error based on exception type
    /// </summary>
    private static List<ValidationError> CreateMemberIdValidationError(Exception exception, string memberId) =>
        exception switch
        {
            MemberNotFoundException =>
                [Errors.MemberNotFound("memberId", memberId, "Member ID")],
            PolicyMemberExistsException ex =>
                [Errors.MemberIdTaken("memberId", memberId, ex.PolicyMemberId.ToString(), "Member ID")],
            MemberNotInContractHolderException ex =>
                [Errors.MemberNotInContractHolder("memberId", memberId, ex.PolicyId ?? "unknown", "Member ID")],
            _ => [Errors.MemberNotFound("memberId", memberId, "Member ID")]
        };

    #endregion
}
