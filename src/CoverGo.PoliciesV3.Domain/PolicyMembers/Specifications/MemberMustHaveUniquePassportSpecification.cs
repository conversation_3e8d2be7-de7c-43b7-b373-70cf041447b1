using CoverGo.PoliciesV3.Domain.Common.Constants;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;

/// <summary>
/// - Passport number must be unique within policy scope (within single policy) for policy holder fields
/// - Passport number must be unique within contract holder scope (across all policies for a contract holder) when contract holder policies are provided
/// - Empty or null passport values are not validated for uniqueness
/// - Only considers members in valid endorsement states (excludes canceled/rejected endorsements)
/// - Contract holder scope validation can be disabled via feature flag "SkipContractHolderUniqueRulesWhenAddPolicyMember"
/// </summary>
public class MemberMustHaveUniquePassportSpecification(
    IPolicyMemberUniquenessService policyMemberUniquenessService,
    ILogger<MemberMustHaveUniquePassportSpecification> logger)
    : ISpecification<UniquenessValidationContext>
{
    public string BusinessRuleName => "Member Must Have Unique Passport";
    public string Description => "Validates that member passport numbers are unique within the appropriate scope (policy or contract holder) based on business rules";

    public virtual async Task<Result> IsSatisfiedBy(UniquenessValidationContext context, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(context);

        try
        {
            string? passportNumber = ExtractPassport(context);
            if (passportNumber == null)
            {
                logger.LogDebug("No passport value provided for member validation, skipping passport uniqueness check");
                return Result.Success();
            }

            logger.LogDebug("Validating passport uniqueness for passport: {Passport} in policy: {PolicyId}", passportNumber, context.PolicyId);

            PolicyMemberFieldDefinition? passportFieldDefinition = context.GetFieldDefinition("passportNo");
            if (passportFieldDefinition == null)
            {
                logger.LogDebug("Passport field not found in schema, skipping passport uniqueness validation");
                return Result.Success();
            }

            var validationErrors = new List<ValidationError>();

            List<ValidationError> policyScopeErrors = await ValidatePolicyScopeUniqueness(context, passportNumber, passportFieldDefinition, cancellationToken);
            validationErrors.AddRange(policyScopeErrors);

            if (ShouldValidateContractHolderScope(context))
            {
                List<ValidationError> contractHolderScopeErrors = await ValidateContractHolderScopeUniqueness(context, passportNumber, passportFieldDefinition, cancellationToken);
                validationErrors.AddRange(contractHolderScopeErrors);
            }

            if (validationErrors.Count > 0)
            {
                logger.LogWarning("Passport uniqueness validation failed for passport: {Passport} in policy: {PolicyId}. Errors: {ErrorCount}",
                    passportNumber, context.PolicyId, validationErrors.Count);
                return Result.Failure(validationErrors);
            }

            logger.LogDebug("Passport uniqueness validation passed for passport: {Passport} in policy: {PolicyId}", passportNumber, context.PolicyId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during passport uniqueness validation for policy: {PolicyId}", context.PolicyId);
            throw;
        }
    }

    private static string? ExtractPassport(UniquenessValidationContext context)
    {
        string? passportValue = context.MemberFields.Value.TryGetValue("passportNo", out string? value) ? value : null;
        return string.IsNullOrWhiteSpace(passportValue) ? null : passportValue;
    }

    private bool ShouldValidateContractHolderScope(UniquenessValidationContext context)
    {
        if (context.ContractHolderPolicyIds.Count == 0 || string.IsNullOrWhiteSpace(context.ContractHolderId))
            return false;

        if (context.ShouldSkipContractHolderValidation)
        {
            logger.LogDebug("Skipping contract holder scope passport validation due to business rule");
            return false;
        }

        return true;
    }

    private async Task<List<ValidationError>> ValidatePolicyScopeUniqueness(
        UniquenessValidationContext context,
        string passportNumber,
        PolicyMemberFieldDefinition passportFieldDefinition,
        CancellationToken cancellationToken)
    {
        try
        {
            List<string?> validEndorsementIds = GetValidEndorsementIds(context.Policy);
            var endorsementIdCollection = EndorsementIdCollection.FromStringIds(validEndorsementIds);

            var fieldValues = new Dictionary<string, object> { ["passportNo"] = passportNumber };
            var fieldNames = new List<string> { "passportNo" };

            List<string> duplicateFields = await policyMemberUniquenessService.ValidatePolicyScopeUniquenessAsync(
                (PolicyId)context.PolicyId,
                null, null,
                fieldValues,
                fieldNames,
                endorsementIdCollection,
                cancellationToken);

            var errors = (duplicateFields ?? [])
                .Where(fieldName => fieldName == "passportNo")
                .Select(_ => Errors.UniqueViolation(ValidationConstants.PropertyPaths.PassportNo, passportFieldDefinition.Label, ValidationConstants.Scopes.Policy))
                .ToList();

            if (errors.Count > 0)
            {
                logger.LogWarning("Policy scope passport uniqueness validation failed for passport: {Passport} in policy: {PolicyId}",
                    passportNumber, context.PolicyId);
            }

            return errors;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during policy scope passport uniqueness validation for policy: {PolicyId}", context.PolicyId);
            throw;
        }
    }

    private async Task<List<ValidationError>> ValidateContractHolderScopeUniqueness(
        UniquenessValidationContext context,
        string passportNumber,
        PolicyMemberFieldDefinition passportFieldDefinition,
        CancellationToken cancellationToken)
    {
        try
        {
            var endorsementIdCollection = EndorsementIdCollection.FromEndorsementIds(
                context.ContractHolderScopeEndorsements ?? []);

            var contractHolderPolicyGuids = (context.ContractHolderPolicyIds ?? [])
                .Where(id => Guid.TryParse(id, out _))
                .Select(id => (PolicyId)id)
                .ToList();

            if (contractHolderPolicyGuids.Count == 0)
            {
                logger.LogDebug("No valid contract holder policy IDs provided, skipping contract holder scope passport validation");
                return [];
            }

            var fieldValues = new Dictionary<string, object> { ["passportNo"] = passportNumber };
            var fieldNames = new List<string> { "passportNo" };

            List<string> duplicateFields = await policyMemberUniquenessService.ValidateContractHolderScopeUniquenessAsync(
                null, null,
                contractHolderPolicyGuids,
                endorsementIdCollection,
                fieldValues,
                fieldNames,
                cancellationToken);

            var errors = (duplicateFields ?? [])
                .Where(fieldName => fieldName == "passportNo")
                .Select(_ => Errors.UniqueViolation(ValidationConstants.PropertyPaths.PassportNo, passportFieldDefinition.Label, ValidationConstants.Scopes.ContractHolder))
                .ToList();

            if (errors.Count > 0)
            {
                logger.LogWarning("Contract holder scope passport uniqueness validation failed for passport: {Passport} in contract holder: {ContractHolderId}",
                    passportNumber, context.ContractHolderId);
            }

            return errors;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during contract holder scope passport uniqueness validation for contract holder: {ContractHolderId}", context.ContractHolderId);
            throw;
        }
    }

    private static List<string?> GetValidEndorsementIds(PolicyDto policy)
    {
        var validEndorsementIds = (policy.Endorsements ?? [])
            .Where(e => !EndorsementStatus.DoNotAccountFor.Contains(e.Status))
            .Select(e => e.Id)
            .ToList<string?>();

        validEndorsementIds.Add(null);
        return validEndorsementIds;
    }
}
