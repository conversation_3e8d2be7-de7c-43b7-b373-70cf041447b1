using CoverGo.PoliciesV3.Domain.Common.Constants;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.PoliciesV3.Domain.ValueObjects;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;

/// <summary>
/// - Staff number must be unique within policy scope (within single policy) for policy holder fields
/// - Staff number must be unique within contract holder scope (across all policies for a contract holder) when contract holder policies are provided
/// - Empty or null staff number values are not validated for uniqueness
/// - Only considers members in valid endorsement states (excludes canceled/rejected endorsements)
/// - Contract holder scope validation can be disabled via feature flag "SkipContractHolderUniqueRulesWhenAddPolicyMember"
/// </summary>
public class MemberMustHaveUniqueStaffNumberSpecification(
    IPolicyMemberUniquenessService policyMemberUniquenessService,
    ILogger<MemberMustHaveUniqueStaffNumberSpecification> logger)
    : ISpecification<UniquenessValidationContext>
{
    public string BusinessRuleName => "Member Must Have Unique Staff Number";
    public string Description => "Validates that member staff numbers are unique within the appropriate scope (policy or contract holder) based on business rules";

    public virtual async Task<Result> IsSatisfiedBy(UniquenessValidationContext context, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(context);

        try
        {
            string? staffNumber = ExtractStaffNumber(context);
            if (staffNumber == null)
            {
                logger.LogDebug("Staff number is null or empty for policy: {PolicyId}", context.PolicyId);
                return Result.Success();
            }

            PolicyMemberFieldDefinition? staffNumberFieldDefinition = context.GetFieldDefinition("staffNo");
            if (staffNumberFieldDefinition == null)
            {
                logger.LogDebug("Staff number field not found in schema for policy: {PolicyId}", context.PolicyId);
                return Result.Success();
            }

            var validationErrors = new List<ValidationError>();

            List<ValidationError> policyScopeErrors = await ValidatePolicyScopeUniqueness(context, staffNumber, staffNumberFieldDefinition, cancellationToken);
            validationErrors.AddRange(policyScopeErrors);

            if (ShouldValidateContractHolderScope(context))
            {
                List<ValidationError> contractHolderScopeErrors = await ValidateContractHolderScopeUniqueness(context, staffNumber, staffNumberFieldDefinition, cancellationToken);
                validationErrors.AddRange(contractHolderScopeErrors);
            }

            if (validationErrors.Count > 0)
            {
                logger.LogWarning("Staff number uniqueness validation failed for staff number: {StaffNumber} in policy: {PolicyId}. Errors: {ErrorCount}",
                    staffNumber, context.PolicyId, validationErrors.Count);
                return Result.Failure(validationErrors);
            }

            logger.LogDebug("Staff number uniqueness validation passed for staff number: {StaffNumber} in policy: {PolicyId}", staffNumber, context.PolicyId);
            return Result.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during staff number uniqueness validation for policy: {PolicyId}", context.PolicyId);
            throw;
        }
    }

    private static string? ExtractStaffNumber(UniquenessValidationContext context) =>
        context.GetRawFieldValue("staffNo") is string staffNumber && !string.IsNullOrWhiteSpace(staffNumber)
            ? staffNumber
            : null;

    private bool ShouldValidateContractHolderScope(UniquenessValidationContext context)
    {
        if (context.ContractHolderPolicyIds.Count == 0 || string.IsNullOrWhiteSpace(context.ContractHolderId))
            return false;

        if (context.ShouldSkipContractHolderValidation)
        {
            logger.LogDebug("Skipping contract holder scope staff number validation due to business rule");
            return false;
        }

        return true;
    }

    private async Task<List<ValidationError>> ValidateContractHolderScopeUniqueness(
        UniquenessValidationContext context,
        string staffNumber,
        PolicyMemberFieldDefinition staffNumberFieldDefinition,
        CancellationToken cancellationToken)
    {
        try
        {
            var endorsementIdCollection = EndorsementIdCollection.FromEndorsementIds(
                context.ContractHolderScopeEndorsements ?? []);

            var contractHolderPolicyGuids = (context.ContractHolderPolicyIds ?? [])
                .Where(id => Guid.TryParse(id, out _))
                .Select(id => (PolicyId)id)
                .ToList();

            if (contractHolderPolicyGuids.Count == 0)
            {
                logger.LogDebug("No valid contract holder policy IDs provided, skipping contract holder scope staff number validation");
                return [];
            }

            var fieldValues = new Dictionary<string, object> { ["staffNo"] = staffNumber };
            var fieldNames = new List<string> { "staffNo" };

            List<string> duplicateFields = await policyMemberUniquenessService.ValidateContractHolderScopeUniquenessAsync(
                null, // MemberId is null for new member creation
                null, // PolicyMemberId is null for new member creation
                contractHolderPolicyGuids,
                endorsementIdCollection,
                fieldValues,
                fieldNames,
                cancellationToken);

            var errors = (duplicateFields ?? [])
                .Where(fieldName => fieldName == "staffNo")
                .Select(_ => Errors.UniqueViolation(ValidationConstants.PropertyPaths.StaffNo, staffNumberFieldDefinition.Label, ValidationConstants.Scopes.ContractHolder))
                .ToList();

            if (errors.Count > 0)
            {
                logger.LogWarning("Contract holder scope staff number uniqueness validation failed for staff number: {StaffNumber} in contract holder: {ContractHolderId}",
                    staffNumber, context.ContractHolderId);
            }

            return errors;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during contract holder scope staff number uniqueness validation for contract holder: {ContractHolderId}", context.ContractHolderId);
            throw;
        }
    }

    private async Task<List<ValidationError>> ValidatePolicyScopeUniqueness(
        UniquenessValidationContext context,
        string staffNumber,
        PolicyMemberFieldDefinition staffNumberFieldDefinition,
        CancellationToken cancellationToken)
    {
        try
        {
            List<string?> validEndorsementIds = GetValidEndorsementIds(context.Policy);
            var endorsementIdCollection = EndorsementIdCollection.FromStringIds(validEndorsementIds);

            var fieldValues = new Dictionary<string, object> { ["staffNo"] = staffNumber };
            var fieldNames = new List<string> { "staffNo" };

            List<string> duplicateFields = await policyMemberUniquenessService.ValidatePolicyScopeUniquenessAsync(
                (PolicyId)context.PolicyId,
                null, null,
                fieldValues,
                fieldNames,
                endorsementIdCollection,
                cancellationToken);

            var errors = (duplicateFields ?? [])
                .Where(fieldName => fieldName == "staffNo")
                .Select(_ => Errors.UniqueViolation(ValidationConstants.PropertyPaths.StaffNo, staffNumberFieldDefinition.Label, ValidationConstants.Scopes.Policy))
                .ToList();

            if (errors.Count > 0)
            {
                logger.LogWarning("Policy scope staff number uniqueness validation failed for staff number: {StaffNumber} in policy: {PolicyId}",
                    staffNumber, context.PolicyId);
            }

            return errors;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during policy scope staff number uniqueness validation for policy: {PolicyId}", context.PolicyId);
            throw;
        }
    }

    private static List<string?> GetValidEndorsementIds(PolicyDto policy)
    {
        var validEndorsementIds = (policy.Endorsements ?? [])
            .Where(e => !EndorsementStatus.DoNotAccountFor.Contains(e.Status))
            .Select(e => e.Id)
            .ToList<string?>();

        validEndorsementIds.Add(null);
        return validEndorsementIds;
    }
}