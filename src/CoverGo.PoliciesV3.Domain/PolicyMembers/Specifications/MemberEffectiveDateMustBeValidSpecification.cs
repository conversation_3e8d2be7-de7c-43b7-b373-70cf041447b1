using System.Globalization;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;

public class MemberEffectiveDateMustBeValidSpecification(
    ILogger<MemberEffectiveDateMustBeValidSpecification> logger)
    : ISpecification<FieldValidationContext>
{
    public string BusinessRuleName => "Member Effective Date Must Be Valid";
    public string Description => "Validates that member effective dates are within policy date ranges and comply with endorsement movement rules";

    public virtual async Task<Result> IsSatisfiedBy(FieldValidationContext context, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(context);

        try
        {
            await Task.CompletedTask;

            DateOnly? effectiveDate = ExtractEffectiveDate(context);
            if (!effectiveDate.HasValue)
            {
                logger.LogDebug("No effective date field found or value is null for policy: {PolicyId}", context.Policy.Id);
                return Result.Success();
            }

            bool isValid = ValidateAgainstPolicyDates(effectiveDate.Value, context) &&
                         ValidateAgainstEndorsementRules(effectiveDate.Value, context);

            if (!isValid)
            {
                logger.LogWarning("Effective date validation failed for date: {EffectiveDate} in policy: {PolicyId}",
                    effectiveDate.Value, context.Policy.Id);
                return Result.Failure(Errors.EffectiveDateOutsidePolicyDates(PolicyMemberUploadWellKnowFields.EffectiveDateField, context.Policy.Id, "Effective Date"));
            }

            logger.LogDebug("Effective date validation passed for date: {EffectiveDate} in policy: {PolicyId}",
                effectiveDate.Value, context.Policy.Id);
            return Result.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during effective date validation for policy: {PolicyId}", context.Policy.Id);
            throw;
        }
    }

    private static DateOnly? ExtractEffectiveDate(FieldValidationContext context)
    {
        var fieldsDict = (context.MemberFields.Value ?? new Dictionary<string, string?>()).ToDictionary(it => it.Key, it => (object?)it.Value);

        return !fieldsDict.TryGetValue(PolicyMemberUploadWellKnowFields.EffectiveDateField, out object? effectiveDateObj) || effectiveDateObj == null
            ? null
            : effectiveDateObj switch
            {
                DateOnly dateOnly => dateOnly,
                string dateString when !string.IsNullOrWhiteSpace(dateString) && DateOnly.TryParse(dateString, CultureInfo.InvariantCulture, out DateOnly parsed) => parsed,
                _ => null
            };
    }

    private static bool ValidateAgainstPolicyDates(DateOnly effectiveDate, FieldValidationContext context) =>
        effectiveDate >= context.Policy.StartDate && effectiveDate <= context.Policy.EndDate;

    private static bool ValidateAgainstEndorsementRules(DateOnly effectiveDate, FieldValidationContext context) =>
        !context.EndorsementId.HasValue || context.Policy.CanChangeMembersViaMovement(context.EndorsementId.Value, effectiveDate);
}
