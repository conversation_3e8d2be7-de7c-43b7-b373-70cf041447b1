using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.Common.Constants;
using CoverGo.PoliciesV3.Domain.Services;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications;

/// <summary>
/// Business Rules:
/// - Dependent members must have a valid primary member (employee) either in the policy or in the upload file
/// - Only applies to dependent members (memberType = "dependent")
/// - Validates dependentOf field against policy members and upload members
/// - Gracefully handles missing primary member data
/// </summary>
public class DependentMustHaveValidPrimaryMemberSpecification(
    IPolicyMemberQueryService policyMemberQueryService,
    ILogger<DependentMustHaveValidPrimaryMemberSpecification> logger)
    : ISpecification<DependentValidationContext>
{
    public string BusinessRuleName => "Dependent Must Have Valid Primary Member";
    public string Description => "Validates that dependent members have a valid primary member either in the policy or in the upload file";

    public virtual async Task<Result> IsSatisfiedBy(DependentValidationContext context, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(context);

        try
        {
            // Non-dependent members (employees, etc.) don't need primary member validation
            if (!context.IsDependent())
            {
                logger.LogDebug("Member is not a dependent - validation skipped");
                return Result.Success();
            }

            string? dependentOf = context.GetDependentOf();

            if (!string.IsNullOrWhiteSpace(dependentOf))
            {
                // Dependent has dependentOf field - validate the primary member exists in policy
                return await ValidatePrimaryMemberInCache(dependentOf, context, cancellationToken);
            }

            // Dependent has no dependentOf field - validate primary member exists in upload
            return ValidatePrimaryMemberInUpload(context);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during dependent primary member validation for policy: {PolicyId}", context.PolicyId);
            return Result.Failure(Errors.NotFound("dependentOf", ValidationConstants.Labels.PrimaryMember));
        }
    }

    private async Task<Result> ValidatePrimaryMemberInCache(string dependentOf, DependentValidationContext context, CancellationToken cancellationToken)
    {
        if (context.IsMemberCached(dependentOf))
        {
            PolicyMember? currentPrimaryMember = context.GetCachedMember(dependentOf);
            if (currentPrimaryMember == null)
            {
                logger.LogDebug("No current policy member state found for dependentOf: {DependentOf}", dependentOf);
                return Result.Failure(Errors.NotFound("dependentOf", ValidationConstants.Labels.PrimaryMember));
            }

            logger.LogDebug("Found primary member {DependentOf} in cache", dependentOf);
            return Result.Success();
        }

        return await QueryPrimaryMemberIndividually(dependentOf, context, cancellationToken);
    }

    private async Task<Result> QueryPrimaryMemberIndividually(string dependentOf, DependentValidationContext context, CancellationToken cancellationToken)
    {
        logger.LogWarning("DependentOf member {DependentOf} not found in pre-resolved cache (cache has {CacheCount} entries), falling back to individual query. " +
            "This should be rare with batch resolution enabled.", dependentOf, context.MembersCache.Count);

        try
        {
            PolicyMember? currentPrimaryMember = await policyMemberQueryService.GetPolicyMemberCurrentStateAsync(
                dependentOf, context.CachedPolicyId, context.CachedEndorsementIdCollection, cancellationToken);

            if (currentPrimaryMember == null)
            {
                logger.LogDebug("No current policy member state found for dependentOf: {DependentOf} via individual query", dependentOf);
                return Result.Failure(Errors.NotFound("dependentOf", ValidationConstants.Labels.PrimaryMember));
            }

            logger.LogDebug("Found primary member {DependentOf} via individual query (fallback)", dependentOf);
            return Result.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during fallback individual query for primary member {DependentOf}", dependentOf);
            return Result.Failure(Errors.NotFound("dependentOf", ValidationConstants.Labels.PrimaryMember));
        }
    }

    private Result ValidatePrimaryMemberInUpload(DependentValidationContext context)
    {
        if (!context.HasPrimaryMemberInUpload())
        {
            logger.LogDebug("Dependent member has no dependentOf field and no primary member in upload");
            return Result.Failure(Errors.NotFound("dependentOf", ValidationConstants.Labels.PrimaryMember));
        }

        logger.LogDebug("Dependent member validation passed - primary member found in upload");
        return Result.Success();
    }
}
