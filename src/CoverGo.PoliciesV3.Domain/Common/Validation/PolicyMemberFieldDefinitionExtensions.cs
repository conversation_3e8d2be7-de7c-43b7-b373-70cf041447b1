using CoverGo.PoliciesV3.Domain.CustomFields;

namespace CoverGo.PoliciesV3.Domain.Common.Validation;

/// <summary>
/// Extension methods for PolicyMemberFieldDefinition to integrate with the new validation system.
/// These methods provide a convenient way to create validation errors for field definitions.
/// </summary>
public static class PolicyMemberFieldDefinitionExtensions
{
    /// <summary>
    /// Creates a required field validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <returns>A validation error indicating the field is required</returns>
    public static ValidationError CreateRequiredError(this PolicyMemberFieldDefinition field) => Errors.Required(field.Name, field.GetFullLabel());

    /// <summary>
    /// Creates an invalid format validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <returns>A validation error indicating invalid format</returns>
    public static ValidationError CreateInvalidFormatError(this PolicyMemberFieldDefinition field) => Errors.InvalidFormat(field.Name, field.GetFullLabel());

    /// <summary>
    /// Creates an invalid type validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <returns>A validation error indicating invalid type</returns>
    public static ValidationError CreateInvalidTypeError(this PolicyMemberFieldDefinition field) => Errors.InvalidType(field.Name, field.GetFullLabel());

    /// <summary>
    /// Creates an invalid option validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <param name="availableOptions">The available options</param>
    /// <returns>A validation error indicating invalid option</returns>
    public static ValidationError CreateInvalidOptionError(this PolicyMemberFieldDefinition field, IEnumerable<string> availableOptions) => Errors.InvalidOption(field.Name, availableOptions, field.GetFullLabel());

    /// <summary>
    /// Creates an invalid string validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <param name="validation">The validation rule that failed</param>
    /// <returns>A validation error indicating invalid string</returns>
    public static ValidationError CreateInvalidStringError(this PolicyMemberFieldDefinition field, string validation) => Errors.InvalidString(field.Name, validation, field.GetFullLabel());

    /// <summary>
    /// Creates an invalid number validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <returns>A validation error indicating invalid number</returns>
    public static ValidationError CreateInvalidNumberError(this PolicyMemberFieldDefinition field) => Errors.InvalidNumber(field.Name, field.GetFullLabel());

    /// <summary>
    /// Creates a not allowed validation error for this field definition.
    /// </summary>
    /// <param name="field">The field definition</param>
    /// <returns>A validation error indicating the field is not allowed</returns>
    public static ValidationError CreateNotAllowedError(this PolicyMemberFieldDefinition field) => Errors.NotAllowed(field.Name, field.GetFullLabel());
}
