using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using CoverGo.PoliciesV3.Application.Services;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Common.Specifications;
using CoverGo.PoliciesV3.Domain.Common.Specifications.Contexts;
using CoverGo.PoliciesV3.Domain.Common.Validation;
using CoverGo.PoliciesV3.Domain.CustomFields;
using CoverGo.PoliciesV3.Domain.Endorsements;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.Policies.Exceptions;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMembers.Specifications.Composite;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.Services;
using Microsoft.Extensions.Logging;
using static CoverGo.PoliciesV3.Application.Services.PolicyMemberValidationDataService;

namespace CoverGo.PoliciesV3.Application.PolicyMembers.CreatePolicyMember;

public class CreatePolicyMemberHandler(
    IPaginatedRepository<PolicyMember, PolicyMemberId> policyMemberRepository,
    ILegacyPolicyService legacyPolicyService,
    IndividualMemberValidationSpecification individualMemberValidationSpec,
    IUsersService usersService,
    IPolicyMemberQueryService policyMemberQueryService,
    ILogger<CreatePolicyMemberHandler> logger,
    PolicyMemberValidationDataService validationDataService)
    : ICommandHandler<CreatePolicyMemberCommand, CreatePolicyMemberResponse>
{
    public async Task<CreatePolicyMemberResponse> Handle(CreatePolicyMemberCommand command, CancellationToken cancellationToken)
    {
        // Ensure policy exists
        Result<PolicyDto> policyResult = await FindPolicyByIdAsync(command.PolicyId.ToString(), cancellationToken);
        if (policyResult.IsFailure)
            throw new PolicyNotFoundException(command.PolicyId.ToString());
        PolicyDto policy = policyResult.Value;

        // 1. GATHER REQUIRED INFORMATION (fails fast if not found)
        (ResolvedValidationData resolvedData, PolicyMemberFieldsSchema schema, MemberUploadFields memberData) = await GatherValidationDataAsync(policy, command, cancellationToken);

        // 2. VALIDATE DOMAIN STATE EARLY (fails fast if invalid)
        validationDataService.ValidatePolicyState(policy, null);

        // 5. CREATE VALIDATION CONTEXT AND EXECUTE (guaranteed valid context)
        var validationContext = IndividualMemberValidationContext.Create(policy, new([memberData]), schema, resolvedData);
        await individualMemberValidationSpec.EvaluateBusinessRulesAsync(validationContext, cancellationToken);

        // Create policy member
        PolicyMember policyMember = PolicyMember.Create(
            command.PolicyId,
            command.MemberId,
            command.StartDate,
            command.EndDate,
            command.PlanId,
            command.DependentOfId.HasValue ? (PolicyMemberId)command.DependentOfId.Value : null,
            command.IndividualId,
            command.Fields
        );

        // Save to repository
        await policyMemberRepository.InsertAsync(policyMember, cancellationToken);

        // Return response
        return new CreatePolicyMemberResponse
        {
            PolicyMemberId = policyMember.Id.Value,
            MemberId = policyMember.MemberId,
            PolicyId = command.PolicyId
        };
    }

    #region Data Gathering for Validation

    /// <summary>
    /// Gathers all the data needed for validation rules to run efficiently for a single member.
    /// This includes feature flags, product information, and member data.
    /// </summary>
    private async Task<(ResolvedValidationData resolvedData, PolicyMemberFieldsSchema schema, MemberUploadFields memberData)> GatherValidationDataAsync(
        PolicyDto policy,
        CreatePolicyMemberCommand command,
        CancellationToken cancellationToken)
    {
        try
        {
            // 1. GATHER FEATURE FLAGS AND PRODUCT DATA (shared service)
            (bool[] featureFlags, IReadOnlyList<string>? availablePlans, string? packageType, List<string>? contractHolderPolicies) =
                await validationDataService.GatherFeatureFlagsAndProductDataAsync(policy, cancellationToken);

            // 2. GET SCHEMA (shared service)
            PolicyMemberFieldsSchema schema = await validationDataService.GetDataSchemaForUploadAsync(policy, null, cancellationToken);

            // 3. PROCESS SINGLE MEMBER DATA
            MemberUploadFields memberData = CreateSingleMemberData(command);

            // 4. Gather member-specific validation data (remains handler-specific)
            MemberDataResults memberDataResults = await GatherMemberSpecificDataAsync(memberData, policy, cancellationToken);

            // 5. CREATE RESOLVED VALIDATION DATA (shared service)
            List<EndorsementId> contractHolderScopeEndorsements = await validationDataService.GetContractHolderScopeEndorsementsAsync(
                contractHolderPolicies ?? [], policy.IsV2, cancellationToken);

            ResolvedValidationData resolvedData = validationDataService.CreateResolvedValidationData(
                policy,
                featureFlags,
                availablePlans,
                packageType,
                contractHolderPolicies,
                contractHolderScopeEndorsements,
                memberDataResults,
                [], // validEndorsementIds, adjust as needed
                memberDataResults.DependentMembersCache);

            logger.LogDebug("Gathered validation data: {FeatureFlags} feature flags, {Plans} available plans, SME: {IsSme}, Member: {MemberId}",
                featureFlags.Length, availablePlans?.Count ?? 0, resolvedData.IsProductSme, command.MemberId);

            return (resolvedData, schema, memberData);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error gathering validation data for policy {PolicyId}", policy.Id);
            throw;
        }
    }

    /// <summary>
    /// Helper to transform CreatePolicyMemberCommand into MemberUploadFields for a single member
    /// </summary>
    private static MemberUploadFields CreateSingleMemberData(CreatePolicyMemberCommand command)
    {
        var memberDict = new Dictionary<string, string?>
        {
            ["memberId"] = command.MemberId,
            ["planId"] = command.PlanId,
            ["startDate"] = command.StartDate?.ToString(),
            ["endDate"] = command.EndDate?.ToString(),
            ["dependentOfId"] = command.DependentOfId?.ToString(),
            ["individualId"] = command.IndividualId?.ToString(),
        };
        // Flatten command.Fields (ICollection<PolicyField>)
        if (command.Fields != null)
        {
            foreach (PolicyField field in command.Fields)
            {
                memberDict[field.Key] = field.Value?.ToString();
            }
        }
        return new MemberUploadFields(memberDict);
    }

    private async Task<Result<PolicyDto>> FindPolicyByIdAsync(string policyId, CancellationToken cancellationToken)
    {
        try
        {
            PolicyDto? policy = await legacyPolicyService.GetPolicyById(policyId, cancellationToken);
            return policy?.Id != null
                ? Result<PolicyDto>.Success(policy)
                : Result<PolicyDto>.Failure(Errors.NotFound("policy", policyId, "Policy"));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to find policy with ID {PolicyId}", policyId);
            throw;
        }
    }

    /// <summary>
    /// Gathers member-specific validation data for a single member (no upload/file logic)
    /// </summary>
    private async Task<MemberDataResults> GatherMemberSpecificDataAsync(
        MemberUploadFields memberData,
        PolicyDto policy,
        CancellationToken cancellationToken)
    {
        if (memberData == null)
            return MemberDataResults.Empty();

        // Extract single member ID
        string? memberId = memberData?.Value.TryGetValue("memberId", out string? id) == true ? id : null;
        if (string.IsNullOrEmpty(memberId))
        {
            return MemberDataResults.Empty();
        }

        logger.LogDebug("Gathering member-specific data for member ID: {MemberId}", memberId);

        // 1. GATHER CORE MEMBER DATA (shared service)
        MemberDataResults memberDataResults = await validationDataService.GatherMemberDataAsync(
            [memberId], policy, null, usersService, policyMemberQueryService, cancellationToken);

        // 2. GATHER DEPENDENT MEMBER (if applicable)
        string? dependentOfId = memberData?.Value.TryGetValue("dependentOfId", out string? depId) == true ? depId : null;
        Dictionary<string, PolicyMember?> dependentMembersCache = !string.IsNullOrEmpty(dependentOfId)
            ? await GatherDependentMembersCacheAsync(
                [dependentOfId], policy, null, policyMemberQueryService, cancellationToken)
            : [];

        // 3. CREATE FINAL MEMBER DATA RESULTS WITH DEPENDENT CACHE
        return new MemberDataResults(
            memberDataResults.IndividualExistenceMap,
            memberDataResults.ExistingPolicyMembers,
            memberDataResults.MemberValidationStates,
            dependentMembersCache);
    }

    #endregion
}
