using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Application.Services.Interfaces;

public interface IPolicyMemberUploadValidationErrorExporterService
{
    Task<byte[]> ExportErrorMembers(string policyMemberUploadId, CancellationToken cancellationToken);
    Task<byte[]> ExportValidationErrors(string policyMemberUploadId, CancellationToken cancellationToken);
    Task PersistValidationErrorsAsync(ICollection<PolicyMemberUploadValidationError> validationErrors, CancellationToken cancellationToken);
}

