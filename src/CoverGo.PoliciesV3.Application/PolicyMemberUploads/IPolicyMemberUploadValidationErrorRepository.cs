using CoverGo.BuildingBlocks.Application.Core.Ports;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;

namespace CoverGo.PoliciesV3.Application.PolicyMemberUploads;

/// <summary>
/// Specialized repository for PolicyMemberUploadValidationError with optimized bulk operations.
/// Extends the generic repository with performance-optimized methods for handling large volumes of validation errors.
/// </summary>
public interface IPolicyMemberUploadValidationErrorRepository : IPaginatedRepository<PolicyMemberUploadValidationError, PolicyMemberUploadValidationErrorId>
{
    /// <summary>
    /// Efficiently inserts a large collection of validation errors using optimized bulk insert operations.
    /// This method uses PostgreSQL-specific optimizations to avoid concurrency issues and improve performance.
    /// </summary>
    /// <param name="validationErrors">Collection of validation errors to persist</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task BulkInsertOptimizedAsync(
        ICollection<PolicyMemberUploadValidationError> validationErrors,
        CancellationToken cancellationToken = default);
}
