namespace CoverGo.PoliciesV3.Application.Common.Interfaces;

public interface IFileParser
{
    FileParseResult ParseFile(byte[] fileContent);

    Task<FileParseResult> ParseFileAsync(byte[] fileContent, CancellationToken cancellationToken = default);
}

public sealed class FileParseResult
{

    /// <summary>
    /// Gets the headers from the file
    /// </summary>
    public required IReadOnlyList<string> Headers { get; init; }

    /// <summary>
    /// Gets the content rows as key-value pairs
    /// </summary>
    public required IReadOnlyList<IReadOnlyDictionary<string, string?>> Contents { get; init; }

    /// <summary>
    /// Gets the headers as a HashSet with case-insensitive comparison for efficient lookups
    /// </summary>
    public HashSet<string> HeadersSet { get; }

    /// <summary>
    /// Gets the number of content rows (excluding header)
    /// </summary>
    public int Count => Contents.Count;

    public FileParseResult()
    {
        HeadersSet = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
    }

    public void InitializeHeadersSet()
    {
        HeadersSet.Clear();
        if (Headers != null)
        {
            foreach (string header in Headers)
            {
                HeadersSet.Add(header);
            }
        }
    }
}