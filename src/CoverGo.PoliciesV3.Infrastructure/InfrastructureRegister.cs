using CoverGo.BuildingBlocks.DataAccess.Mongo;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql.Caching;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql.Constants;
using CoverGo.BuildingBlocks.MessageBus;
using CoverGo.BuildingBlocks.MessageBus.Dapr.TenantContext;
using CoverGo.BuildingBlocks.Scheduler.Hangfire;
using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.Policies;
using CoverGo.PoliciesV3.Domain.PolicyMembers;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.Services;
using CoverGo.PoliciesV3.Infrastructure.Authentication.Auth;
using CoverGo.PoliciesV3.Infrastructure.Authentication.Auth.Interface;
using CoverGo.PoliciesV3.Infrastructure.Common.Resilience;
using CoverGo.PoliciesV3.Infrastructure.CustomFields;
using CoverGo.PoliciesV3.Infrastructure.DataAccess;
using CoverGo.PoliciesV3.Infrastructure.ExternalServices;
using CoverGo.PoliciesV3.Infrastructure.FileProcessing;
using CoverGo.PoliciesV3.Infrastructure.Policies;
using CoverGo.PoliciesV3.Infrastructure.PolicyMembers;
using CoverGo.PoliciesV3.Infrastructure.PolicyMemberUploads;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace CoverGo.PoliciesV3.Infrastructure;

/// <summary>
/// Provides extension methods for registering infrastructure services in the dependency injection container.
/// </summary>
public static class InfrastructureRegister
{
    /// <summary>
    /// Registers all infrastructure services including caching, database, repositories, external services, and domain services.
    /// </summary>
    /// <param name="services">The service collection to add services to.</param>
    /// <param name="configuration">The application configuration.</param>
    /// <returns>The service collection for chaining.</returns>
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddCaching(configuration);
        services.AddDatabase(configuration);
        services.AddRepositories();
        services.AddMessageBusServices();
        services.AddFileServices();
        services.AddFeatureManagement();
        services.AddExternalHttpServices();
        services.AddDomainServices();
        services.AddUserContextServices();

        return services;
    }

    /// <summary>
    /// Registers minimal message bus services required for JWT authentication handlers.
    /// This provides the IMessageContextAccessor dependency without full message bus setup.
    /// </summary>
    /// <param name="services">The service collection to add services to.</param>
    /// <returns>The service collection for chaining.</returns>
    private static IServiceCollection AddMessageBusServices(this IServiceCollection services)
    {
        // Add minimal message bus services for JWT handlers
        services.AddMessageBus();

        return services;
    }

    /// <summary>
    /// Registers caching services including Redis distributed cache and cache providers.
    /// </summary>
    /// <param name="services">The service collection to add services to.</param>
    /// <param name="configuration">The application configuration.</param>
    /// <returns>The service collection for chaining.</returns>
    private static IServiceCollection AddCaching(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddStackExchangeRedisCache(options =>
        {
            options.Configuration = configuration.GetConnectionString(ConfigurationConstants.ConnectionStrings.Redis) ??
                                   throw new InvalidOperationException("Redis connection string is not configured.");
            options.InstanceName = "covergo-policies-v3:";
        });

        services.AddSingleton<CacheProvider>();

        return services;
    }

    /// <summary>
    /// Registers database context and unit of work services.
    /// </summary>
    /// <param name="services">The service collection to add services to.</param>
    /// <param name="configuration">The application configuration.</param>
    /// <returns>The service collection for chaining.</returns>
    private static IServiceCollection AddDatabase(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddDatabase<ApplicationDbContext>(configuration);
        return services;
    }

    /// <summary>
    /// Registers repository services including base PostgreSQL repositories and specialized repositories.
    /// </summary>
    /// <param name="services">The service collection to add services to.</param>
    /// <returns>The service collection for chaining.</returns>
    private static IServiceCollection AddRepositories(this IServiceCollection services)
    {
        // Register base PostgreSQL repositories
        // Set useCache to true for repositories that require caching
        // Need to consider carefully when enabling caching for each repository
        services.AddPostgreSqlRepository<Policy, PolicyId, ApplicationDbContext>();
        services.AddPostgreSqlRepository<PolicyMember, PolicyMemberId, ApplicationDbContext>();
        services.AddPostgreSqlRepository<PolicyMemberUpload, PolicyMemberUploadId, ApplicationDbContext>();
        services.AddPostgreSqlRepository<PolicyMemberUploadValidationError, PolicyMemberUploadValidationErrorId, ApplicationDbContext>();

        // Register specialized repositories with business-specific methods
        services.AddScoped<IPolicyMemberUploadRepository, PolicyMemberUploadRepository>();
        services.AddScoped<IPolicyMemberDataRepository, PolicyMemberDataRepository>();
        services.AddScoped<IPolicyMemberUploadValidationErrorRepository, PolicyMemberUploadValidationErrorRepository>();

        // Register schema services
        services.AddScoped<IPolicyMemberFieldsSchemaRepository, PolicyMemberFieldsSchemaRepository>();
        services.AddScoped<IPolicyMemberFieldsSchemaProvider, PolicyMemberFieldsSchemaProvider>();

        // Decorate IPolicyMemberFieldsSchemaProvider with caching
        services.Decorate<IPolicyMemberFieldsSchemaProvider, CachedPolicyMemberFieldsSchemaProvider>();

        return services;
    }

    /// <summary>
    /// Registers file parsing and processing services.
    /// </summary>
    /// <param name="services">The service collection to add services to.</param>
    /// <returns>The service collection for chaining.</returns>
    private static IServiceCollection AddFileServices(this IServiceCollection services)
    {
        services.AddSingleton<IFileParserFactory, FileParserFactory>();
        services.AddScoped<IFileProcessingService, FileProcessingService>();

        return services;
    }

    /// <summary>
    /// Registers feature management services for multi-tenant feature flags.
    /// </summary>
    /// <param name="services">The service collection to add services to.</param>
    /// <returns>The service collection for chaining.</returns>
    private static IServiceCollection AddFeatureManagement(this IServiceCollection services)
    {
        services.AddSingleton<IMultiTenantFeatureManager, MultiTenantFeatureManager>();
        services.AddScoped<TenantId>(provider =>
        {
            ITenantProvider tenantProvider = provider.GetRequiredService<ITenantProvider>();
            if (tenantProvider.TryGetCurrent(out TenantId? tenantId))
            {
                return tenantId;
            }
            throw new InvalidOperationException("Current tenant is not set.");
        });
        return services;
    }

    /// <summary>
    /// Registers external HTTP services with resilience patterns and contextual JWT authentication.
    /// </summary>
    /// <param name="services">The service collection to add services to.</param>
    /// <returns>The service collection for chaining.</returns>
    private static IServiceCollection AddExternalHttpServices(this IServiceCollection services)
    {
        services.AddHttpClients();
        services.AddHttpClientAuthentication();
        services.AddHttpServiceDecorators();

        return services;
    }

    /// <summary>
    /// Registers domain services for business logic operations.
    /// </summary>
    /// <param name="services">The service collection to add services to.</param>
    /// <returns>The service collection for chaining.</returns>
    private static IServiceCollection AddDomainServices(this IServiceCollection services)
    {
        services.AddScoped<IPolicyMemberUniquenessService, PolicyMemberUniquenessService>();
        services.AddScoped<IPolicyMemberQueryService, PolicyMemberQueryService>();
        services.AddScoped<IPolicyMemberUploadValidationErrorExporterService, PolicyMemberUploadValidationErrorExporterService>();

        return services;
    }

    /// <summary>
    /// Registers user context services for audit information.
    /// </summary>
    /// <param name="services">The service collection to add services to.</param>
    /// <returns>The service collection for chaining.</returns>
    private static IServiceCollection AddUserContextServices(this IServiceCollection services)
    {
        // Register HTTP context accessor for user context
        services.AddHttpContextAccessor();

        return services;
    }

    /// <summary>
    /// Registers Hangfire scheduler with MongoDB storage for background job processing.
    /// Uses a single 'policies' database without tenant isolation for job storage.
    /// </summary>
    /// <param name="services">The service collection to add services to.</param>
    /// <param name="configuration">The application configuration.</param>
    /// <returns>The service collection for chaining.</returns>
    public static IServiceCollection AddHangfireSchedulerForJobs(this IServiceCollection services, IConfiguration configuration)
    {
        string dbConnectionString = Environment.GetEnvironmentVariable("DATABASE_CONNECT_STRING") ??
                                   configuration?.GetConnectionString(ConfigurationConstants.ConnectionStrings.Mongo) ??
                                   throw new InvalidOperationException(
                                       "MongoDB connection string is not configured.");

        services.AddMongoDb(options =>
        {
            options.ConnectionString = dbConnectionString;
            options.DatabaseName = "policies";
            options.UseTransactions = false;
            options.IgnoreDatabaseNamingConvention = true;
        });

        services.AddHangfireScheduler(configuration);

        return services;
    }

    /// <summary>
    /// Registers HTTP clients with comprehensive configuration including resilience patterns, header propagation,
    /// tenant context, and JWT authentication for external services.
    ///
    /// Each HTTP client is configured with:
    /// 1. Base address configuration from appsettings
    /// 2. Resilience policies (retry, circuit breaker, timeout)
    /// 3. Header propagation for request context
    /// 4. Tenant ID propagation for multi-tenant support
    /// 5. JWT authentication handlers for contextual authentication
    ///
    /// JWT handlers automatically determine authentication method based on execution context:
    /// - API calls: No authentication (handlers inactive)
    /// - Hangfire jobs with message context: Token propagation via JwtDelegatingHandler
    /// - Autonomous Hangfire jobs: Service account auth via JwtServiceDelegatingHandler
    /// </summary>
    /// <param name="services">The service collection to add services to.</param>
    /// <returns>The service collection for chaining.</returns>
    private static IServiceCollection AddHttpClients(this IServiceCollection services)
    {
        IResilienceHttpBuilder resilienceBuilder = services.AddResilienceHttp();

        // Configure Users Service HTTP client with complete pipeline
        resilienceBuilder
            .AddHttpClient<IUsersService, UsersService>("users", ConfigureHttpClient("users"))
            .AddHeaderPropagation()
            .AddTenantIdPropagation()
            .AddHttpMessageHandler<JwtDelegatingHandler>();

        // Configure Cases Service HTTP client with complete pipeline
        resilienceBuilder
            .AddHttpClient<ICasesService, CasesService>("cases", ConfigureHttpClient("cases"))
            .AddHeaderPropagation()
            .AddTenantIdPropagation()
            .AddHttpMessageHandler<JwtDelegatingHandler>();

        // Configure FileSystem Service HTTP client with complete pipeline
        resilienceBuilder
            .AddHttpClient<IFileSystemService, FileSystemService>("filesystem", ConfigureHttpClient("filesystem"))
            .AddHeaderPropagation()
            .AddTenantIdPropagation()
            .AddHttpMessageHandler<JwtDelegatingHandler>();

        // Configure Legacy Policy Service HTTP client with complete pipeline
        resilienceBuilder
            .AddHttpClient<ILegacyPolicyService, LegacyPolicyService>("policies", ConfigureHttpClient("policies"))
            .AddHeaderPropagation()
            .AddTenantIdPropagation()
            .AddHttpMessageHandler<JwtDelegatingHandler>();

        // Configure Product Service HTTP client with complete pipeline
        resilienceBuilder
            .AddHttpClient<IProductService, ProductService>("products", ConfigureHttpClient("products"))
            .AddHeaderPropagation()
            .AddTenantIdPropagation()
            .AddHttpMessageHandler<JwtDelegatingHandler>();

        return services;
    }

    /// <summary>
    /// Registers JWT authentication services and handlers for HTTP clients.
    /// </summary>
    /// <param name="services">The service collection to add services to.</param>
    /// <returns>The service collection for chaining.</returns>
    private static IServiceCollection AddHttpClientAuthentication(this IServiceCollection services)
    {
        services.TryAddTransient<JwtDelegatingHandler>();
        services.TryAddTransient<JwtServiceDelegatingHandler>();
        services.AddTransportMessageInterceptor<JwtMessageForwarder>();
        services.TryAddScoped<IJwtTokenFetcher, JwtTokenFetcher>();

        return services;
    }

    /// <summary>
    /// Registers decorators for HTTP services to add cross-cutting concerns like caching.
    /// </summary>
    /// <param name="services">The service collection to add services to.</param>
    /// <returns>The service collection for chaining.</returns>
    private static IServiceCollection AddHttpServiceDecorators(this IServiceCollection services)
    {
        services.Decorate<ICasesService, CachedCasesService>();
        services.Decorate<IProductService, CachedProductService>();
        return services;
    }

    /// <summary>
    /// Creates a configuration action for HTTP clients that sets the base address from configuration.
    /// </summary>
    /// <param name="serviceKey">The service key used to lookup the base URL in configuration.</param>
    /// <returns>An action that configures the HTTP client with the appropriate base address.</returns>
    private static Action<IServiceProvider, HttpClient> ConfigureHttpClient(string serviceKey)
    {
        return (serviceProvider, client) =>
        {
            IConfiguration configuration = serviceProvider.GetRequiredService<IConfiguration>();
            string? baseUrl = configuration[$"serviceUrls:{serviceKey}"];

            if (!string.IsNullOrEmpty(baseUrl))
            {
                if (!Uri.IsWellFormedUriString(baseUrl, UriKind.Absolute))
                {
                    throw new InvalidOperationException($"Invalid URL configured for service '{serviceKey}': {baseUrl}");
                }

                client.BaseAddress = new Uri(baseUrl);
            }
        };
    }
}
