using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using System.Text;
using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace CoverGo.PoliciesV3.Infrastructure.PolicyMemberUploads;

public class PolicyMemberUploadValidationErrorExporterService(
    IFileParserFactory fileParserFactory,
    IFileSystemService fileSystemService,
    IPaginatedRepository<PolicyMemberUpload, PolicyMemberUploadId> policyMemberUploadRepository,
    IPolicyMemberUploadValidationErrorRepository policyMemberUploadValidationErrorRepository,
    ILogger<PolicyMemberUploadValidationErrorExporterService> logger) : IPolicyMemberUploadValidationErrorExporterService
{
    public async Task<byte[]> ExportErrorMembers(string policyMemberUploadId, CancellationToken cancellationToken)
    {
        (string[] headers, IReadOnlyList<IReadOnlyDictionary<string, string?>> contents) = await GetOriginalUploadFile(policyMemberUploadId, cancellationToken);
        var uploadId = (PolicyMemberUploadId)policyMemberUploadId;
        IEnumerable<PolicyMemberUploadValidationError> errors = await policyMemberUploadValidationErrorRepository.FindAllByAsync(
            error => error.PolicyMemberUploadId == uploadId,
            cancellationToken);
        var errorRowIndexes = errors.Select(error => error.RowIndex).ToHashSet();
        string result = BuildErrorMemberCsvString(headers, contents, errorRowIndexes);
        return Encoding.UTF8.GetBytes(result);
    }

    public static string BuildErrorMemberCsvString(string[] headers, IReadOnlyList<IReadOnlyDictionary<string, string?>> contents, HashSet<int> errorRowIndexes)
    {
        var result = new StringBuilder();
        result.AppendLine($"Row Index,{string.Join(',', headers)}");
        for (int rowIndex = 1; rowIndex <= contents.Count; rowIndex++)
        {
            if (errorRowIndexes.Contains(rowIndex))
                result.AppendLine($"{rowIndex},{BuildCsvLine(headers, contents[rowIndex - 1])}");
        }

        return result.ToString();
    }

    public async Task<byte[]> ExportValidationErrors(string policyMemberUploadId, CancellationToken cancellationToken)
    {
        (string[] _, IReadOnlyList<IReadOnlyDictionary<string, string?>> contents) = await GetOriginalUploadFile(policyMemberUploadId, cancellationToken);
        var uploadId = (PolicyMemberUploadId)policyMemberUploadId;
        IEnumerable<PolicyMemberUploadValidationError> errors = await policyMemberUploadValidationErrorRepository.FindAllByAsync(
            error => error.PolicyMemberUploadId == uploadId,
            cancellationToken);

        string result = BuildValidationErrorDetails(contents, errors);
        return Encoding.UTF8.GetBytes(result);
    }

    public static string BuildValidationErrorDetails(IReadOnlyList<IReadOnlyDictionary<string, string?>> contents, IEnumerable<PolicyMemberUploadValidationError> errors)
    {
        var result = new StringBuilder();
        result.AppendLine("Row Index,Name,Staff No.,Passport No.,National ID,Error Code,Error Detail");
        foreach (PolicyMemberUploadValidationError? error in errors.OrderBy(x => x.RowIndex))
        {
            if (error.RowIndex <= 0 || error.RowIndex > contents.Count)
            {
                result.AppendLine($"{error.RowIndex},,,,,{error.Code},{RemoveCommas(error.Message)}");
                continue;
            }

            IReadOnlyDictionary<string, string?> rowData = contents[error.RowIndex - 1];
            rowData.TryGetValue("Name", out string? name);
            rowData.TryGetValue("Staff No.", out string? staffNo);
            rowData.TryGetValue("Passport No.", out string? passportNo);
            rowData.TryGetValue("National ID", out string? hkid);

            string errorMessage = RemoveCommas(error.Message);
            result.AppendLine($"{error.RowIndex},{name ?? string.Empty},{staffNo ?? string.Empty},{passportNo ?? string.Empty},{hkid ?? string.Empty},{error.Code},{errorMessage}");
        }

        return result.ToString();
    }

    /// <summary>
    /// Persists validation errors to the database using optimized bulk insertion for efficiency.
    /// Only persists errors - successful validations do not create database records.
    /// Uses specialized repository with PostgreSQL-optimized bulk insert operations.
    /// </summary>
    /// <param name="validationErrors">Collection of validation errors to persist</param>
    /// <param name="cancellationToken">Cancellation token</param>
    public async Task PersistValidationErrorsAsync(
        ICollection<PolicyMemberUploadValidationError> validationErrors,
        CancellationToken cancellationToken)
    {
        if (validationErrors.Count == 0)
            return;

        var stopwatch = Stopwatch.StartNew();
        try
        {
            logger.LogDebug("Starting to persist {ErrorCount} validation errors to database", validationErrors.Count);

            // Use the specialized repository's optimized bulk insert method
            // This method uses EFCore.BulkExtensions with PostgreSQL COPY BINARY operations
            // to avoid concurrency issues and provide optimal performance
            await policyMemberUploadValidationErrorRepository.BulkInsertOptimizedAsync(validationErrors, cancellationToken);

            stopwatch.Stop();
            logger.LogDebug("Successfully persisted {ErrorCount} validation errors to database in {ElapsedMs}ms",
                validationErrors.Count, stopwatch.ElapsedMilliseconds);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            logger.LogError(ex, "Failed to persist {ErrorCount} validation errors to database after {ElapsedMs}ms",
                validationErrors.Count, stopwatch.ElapsedMilliseconds);
            throw;
        }
    }

    private static string RemoveCommas(string errorMessage) => errorMessage.Contains(',') ? errorMessage.Replace(",", string.Empty) : errorMessage;

    private async Task<(string[] headers, IReadOnlyList<IReadOnlyDictionary<string, string?>> contents)> GetOriginalUploadFile(string policyMemberUploadId, CancellationToken cancellationToken)
    {
        var uploadId = (PolicyMemberUploadId)policyMemberUploadId;
        PolicyMemberUpload upload = await policyMemberUploadRepository.GetByIdAsync(uploadId, cancellationToken) ?? throw new ArgumentException("Members file does not exist.");
        byte[]? fileContent = await fileSystemService.GetFileByPath(upload.Path, cancellationToken);
        if (fileContent == null || fileContent.Length == 0)
            throw new ArgumentException($"Errors while download members file: File not found or empty");

        IFileParser fileParser = fileParserFactory.CreateParser(fileContent);
        FileParseResult parseResult = await fileParser.ParseFileAsync(fileContent, cancellationToken) ?? throw new InvalidOperationException("Failed to parse file content");
        return (parseResult.Headers.ToArray(), parseResult.Contents);
    }

    internal static string BuildCsvLine(string[] headers, IReadOnlyDictionary<string, string?> data)
        => string.Join(',', headers.Select(header => data.TryGetValue(header, out string? value) ? value ?? string.Empty : string.Empty));
}