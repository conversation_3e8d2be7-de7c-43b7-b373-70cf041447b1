using CoverGo.BuildingBlocks.Application.Core.Ports;
using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using CoverGo.PoliciesV3.Application.PolicyMemberUploads;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Infrastructure.DataAccess;
using EFCore.BulkExtensions;
using Microsoft.Extensions.Logging;

namespace CoverGo.PoliciesV3.Infrastructure.PolicyMemberUploads;

/// <summary>
/// Specialized repository implementation for PolicyMemberUploadValidationError with optimized bulk operations.
/// Provides PostgreSQL-specific optimizations for handling large volumes of validation errors efficiently.
/// </summary>
internal class PolicyMemberUploadValidationErrorRepository(
    PostgreSqlUnitOfWork<ApplicationDbContext> unitOfWork,
    IUserContextProvider userContextProvider,
    ILogger<PolicyMemberUploadValidationErrorRepository> logger)
    : PostgreSqlRepository<PolicyMemberUploadValidationError, PolicyMemberUploadValidationErrorId, ApplicationDbContext>(unitOfWork, userContextProvider), 
      IPolicyMemberUploadValidationErrorRepository
{
    /// <summary>
    /// Efficiently inserts a large collection of validation errors using EFCore.BulkExtensions.
    /// This method uses PostgreSQL COPY BINARY operations to avoid concurrency issues and provide optimal performance.
    /// </summary>
    /// <param name="validationErrors">Collection of validation errors to persist</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task BulkInsertOptimizedAsync(
        ICollection<PolicyMemberUploadValidationError> validationErrors,
        CancellationToken cancellationToken = default)
    {
        if (validationErrors.Count == 0)
            return;

        try
        {
            logger.LogDebug("Starting optimized bulk insert for {ErrorCount} validation errors", validationErrors.Count);

            // Convert to list for bulk insertion
            var errorsToInsert = validationErrors.ToList();

            // Configure bulk insert for optimal PostgreSQL performance
            var bulkConfig = new BulkConfig
            {
                BatchSize = 2000, // Optimal batch size for PostgreSQL
                BulkCopyTimeout = 300, // 5 minutes timeout for large datasets
                TrackingEntities = false, // Don't track entities for better performance
                SetOutputIdentity = false, // Not needed for validation errors
                PreserveInsertOrder = false, // Order not important for validation errors
                UseTempDB = false, // Use regular tables for better performance
                WithHoldlock = false // Avoid unnecessary locking
            };

            // Use EFCore.BulkExtensions for true bulk insert with PostgreSQL COPY BINARY
            // This avoids the concurrency issues seen with standard EF Core InsertBatchAsync
            await DbContext.BulkInsertAsync(errorsToInsert, bulkConfig, cancellationToken: cancellationToken);

            logger.LogDebug("Successfully completed optimized bulk insert for {ErrorCount} validation errors", errorsToInsert.Count);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to perform optimized bulk insert for {ErrorCount} validation errors", validationErrors.Count);
            throw;
        }
    }
}
