using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace CoverGo.PoliciesV3.Infrastructure.Common.Resilience;

/// <summary>
/// Helper class for configuring resilience policies for external service clients
/// </summary>
public static class ResilienceRegister
{
    public static IResilienceHttpBuilder AddResilienceHttp(this IServiceCollection services)
    {
        services.AddOptions<ResilienceConfiguration>()
            .BindConfiguration(ResilienceConfiguration.SectionName)
            .ValidateDataAnnotations()
            .ValidateOnStart();
        return new ResilienceHttpBuilder(services);
    }

    /// <summary>
    /// Configures circuit breaker and other resilience policies for an HttpClient
    /// </summary>
    /// <typeparam name="TService">The service type that will use the HttpClient</typeparam>
    /// <typeparam name="TImplementation"></typeparam>
    /// <param name="builder">The resilience HTTP builder</param>
    /// <param name="serviceKey">The service key used for configuration and logging</param>
    /// <param name="configureClient">Action to configure the HTTP client</param>
    /// <returns>The HTTP client builder for chaining additional configurations</returns>
    public static IHttpClientBuilder AddHttpClient<TService, TImplementation>(this IResilienceHttpBuilder builder, string serviceKey, Action<IServiceProvider, HttpClient> configureClient)
        where TService : class
        where TImplementation : class, TService
    {
        IHttpClientBuilder httpClientBuilder = builder.Services.AddHttpClient<TImplementation>(configureClient);

        builder.Services.AddScoped<TService>(serviceProvider =>
            serviceProvider.GetRequiredService<TImplementation>());

        // Configure resilience policies using deferred configuration
        httpClientBuilder.AddStandardResilienceHandler();

        // Configure the resilience options using the options pattern with access to IServiceProvider
        httpClientBuilder.Services.AddOptions<Microsoft.Extensions.Http.Resilience.HttpStandardResilienceOptions>(httpClientBuilder.Name)
            .Configure<IOptions<ResilienceConfiguration>, ILogger<TService>>((options, resilienceOptions, logger) =>
            {
                ResilienceConfiguration resilienceConfiguration = resilienceOptions.Value;

                // Get service-specific overrides
                ServiceResilienceConfiguration serviceConfiguration = resilienceConfiguration.Services.GetValueOrDefault(serviceKey, new ServiceResilienceConfiguration());
                if (serviceConfiguration.Enabled)
                {
                    // Apply service-specific overrides or use global defaults
                    var breakDuration = TimeSpan.FromSeconds(serviceConfiguration.BreakDurationSeconds ?? resilienceConfiguration.BreakDurationSeconds);
                    int minimumThroughput = serviceConfiguration.MinimumThroughput ?? resilienceConfiguration.MinimumThroughput;
                    var samplingDuration = TimeSpan.FromSeconds(serviceConfiguration.SamplingDurationSeconds ?? resilienceConfiguration.SamplingDurationSeconds);
                    double failureRatio = serviceConfiguration.FailureRatio ?? resilienceConfiguration.FailureRatio;

                    // Configure circuit breaker with custom settings
                    options.CircuitBreaker.FailureRatio = failureRatio;
                    options.CircuitBreaker.MinimumThroughput = minimumThroughput;
                    options.CircuitBreaker.SamplingDuration = samplingDuration;
                    options.CircuitBreaker.BreakDuration = breakDuration;

                    // Configure timeout settings to prevent 10-second timeout errors
                    options.AttemptTimeout.Timeout = TimeSpan.FromSeconds(30); // Increase from default 10s to 30s
                    options.TotalRequestTimeout.Timeout = TimeSpan.FromMinutes(2); // Set total request timeout to 2 minutes

                    logger?.LogInformation("Circuit breaker and timeout configured for service {ServiceKey} with " +
                                        "{BreakDuration}s break duration, {FailureRatio:P} failure ratio threshold, " +
                                        "{MinimumThroughput} minimum throughput, {AttemptTimeout}s attempt timeout, " +
                                        "{TotalTimeout}s total timeout",
                        serviceKey, breakDuration.TotalSeconds, failureRatio, minimumThroughput,
                        options.AttemptTimeout.Timeout.TotalSeconds, options.TotalRequestTimeout.Timeout.TotalSeconds);
                }
                else
                {
                    // Even when circuit breaker is disabled, configure timeout settings
                    options.AttemptTimeout.Timeout = TimeSpan.FromSeconds(30); // Increase from default 10s to 30s
                    options.TotalRequestTimeout.Timeout = TimeSpan.FromMinutes(2); // Set total request timeout to 2 minutes

                    logger?.LogInformation("Circuit breaker is disabled for service {ServiceKey}, but timeout configured with " +
                                        "{AttemptTimeout}s attempt timeout, {TotalTimeout}s total timeout",
                        serviceKey, options.AttemptTimeout.Timeout.TotalSeconds, options.TotalRequestTimeout.Timeout.TotalSeconds);
                }
            });

        // Return the original HttpClientBuilder for further chaining
        return httpClientBuilder;
    }
}
