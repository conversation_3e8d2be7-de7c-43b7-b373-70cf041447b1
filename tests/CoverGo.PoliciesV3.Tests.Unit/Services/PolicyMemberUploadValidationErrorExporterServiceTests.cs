using CoverGo.BuildingBlocks.DataAccess.PostgreSql;
using CoverGo.PoliciesV3.Application.Common.Interfaces;
using CoverGo.PoliciesV3.Application.Services.Interfaces;
using CoverGo.PoliciesV3.Domain.PolicyMemberUploads;
using CoverGo.PoliciesV3.Infrastructure.DataAccess;
using CoverGo.PoliciesV3.Infrastructure.PolicyMemberUploads;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Moq;

namespace CoverGo.PoliciesV3.Tests.Unit.Services;

/// <summary>
/// Unit tests for PolicyMemberUploadValidationErrorExporterService methods
/// </summary>
public class PolicyMemberUploadValidationErrorExporterServiceTests
{
    #region BuildErrorMemberCsvString Tests

    [Fact]
    [Trait("Ticket", "CH-18823")]
    public void BuildErrorMemberCsvString_WithValidData_ShouldGenerateCorrectCsv()
    {
        // Arrange
        string[] headers = new[] { "Name", "Staff No." };
        var contents = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Name", "John Doe" }, { "Staff No.", "EMP001" } },
            new Dictionary<string, string?> { { "Name", "Jane Smith" }, { "Staff No.", "EMP002" } },
            new Dictionary<string, string?> { { "Name", "Bob Wilson" }, { "Staff No.", "EMP003" } }
        };
        var errorRowIndexes = new HashSet<int> { 1, 3 };

        // Act
        string result = PolicyMemberUploadValidationErrorExporterService.BuildErrorMemberCsvString(headers, contents, errorRowIndexes);

        // Assert
        string expectedCsv = $"Row Index,Name,Staff No.{Environment.NewLine}" +
                             $"1,John Doe,EMP001{Environment.NewLine}" +
                             $"3,Bob Wilson,EMP003{Environment.NewLine}";

        result.Should().Be(expectedCsv);
    }

    [Fact]
    [Trait("Ticket", "CH-18823")]
    public void BuildErrorMemberCsvString_WithEmptyErrorRows_ShouldReturnOnlyHeader()
    {
        // Arrange
        string[] headers = new[] { "Name", "Staff No." };
        var contents = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Name", "John Doe" }, { "Staff No.", "EMP001" } },
            new Dictionary<string, string?> { { "Name", "Jane Smith" }, { "Staff No.", "EMP002" } }
        };
        var errorRowIndexes = new HashSet<int>(); // No error rows

        // Act
        string result = PolicyMemberUploadValidationErrorExporterService.BuildErrorMemberCsvString(headers, contents, errorRowIndexes);

        // Assert
        string expectedCsv = $"Row Index,Name,Staff No.{Environment.NewLine}";
        result.Should().Be(expectedCsv);
    }

    [Fact]
    [Trait("Ticket", "CH-18823")]
    public void BuildErrorMemberCsvString_WithMissingValues_ShouldHandleNullValues()
    {
        // Arrange
        string[] headers = new[] { "Name", "Staff No." };
        var contents = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Name", "John Doe" }, { "Staff No.", "EMP001" } },
            new Dictionary<string, string?> { { "Name", null }, { "Staff No.", "EMP002" } }
        };
        var errorRowIndexes = new HashSet<int> { 1, 2 };

        // Act
        string result = PolicyMemberUploadValidationErrorExporterService.BuildErrorMemberCsvString(headers, contents, errorRowIndexes);

        // Assert
        string expectedCsv = $"Row Index,Name,Staff No.{Environment.NewLine}" +
                             $"1,John Doe,EMP001{Environment.NewLine}" +
                             $"2,,EMP002{Environment.NewLine}";

        result.Should().Be(expectedCsv);
    }

    [Fact]
    [Trait("Ticket", "CH-18823")]
    public void BuildErrorMemberCsvString_WithAllRowsAsErrors_ShouldIncludeAllRows()
    {
        // Arrange
        string[] headers = new[] { "Name", "Staff No." };
        var contents = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Name", "John Doe" }, { "Staff No.", "EMP001" } },
            new Dictionary<string, string?> { { "Name", "Jane Smith" }, { "Staff No.", "EMP002" } }
        };
        var errorRowIndexes = new HashSet<int> { 1, 2 }; // All rows have errors

        // Act
        string result = PolicyMemberUploadValidationErrorExporterService.BuildErrorMemberCsvString(headers, contents, errorRowIndexes);

        // Assert
        string expectedCsv = $"Row Index,Name,Staff No.{Environment.NewLine}" +
                             $"1,John Doe,EMP001{Environment.NewLine}" +
                             $"2,Jane Smith,EMP002{Environment.NewLine}";

        result.Should().Be(expectedCsv);
    }

    #endregion

    #region BuildValidationErrorDetails Tests

    [Fact]
    [Trait("Ticket", "CH-18824")]
    public void BuildValidationErrorDetails_WithValidData_ShouldGenerateCorrectCsv()
    {
        // Arrange
        var contents = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Name", "John Doe" }, { "Staff No.", "EMP001" }, { "Passport No.", "A123456" }, { "National ID", "HK123456" } },
            new Dictionary<string, string?> { { "Name", "Jane Smith" }, { "Staff No.", "EMP002" }, { "Passport No.", "B789012" }, { "National ID", "HK789012" } },
            new Dictionary<string, string?> { { "Name", "Bob Wilson" }, { "Staff No.", "EMP003" }, { "Passport No.", "C345678" }, { "National ID", "HK345678" } }
        };

        var errors = new List<PolicyMemberUploadValidationError>
        {
            CreateValidationError(1, "INVALID_NAME", "Name is required"),
            CreateValidationError(3, "INVALID_PASSPORT", "Passport number format is invalid")
        };

        // Act
        string result = PolicyMemberUploadValidationErrorExporterService.BuildValidationErrorDetails(contents, errors);

        // Assert
        string expectedCsv = $"Row Index,Name,Staff No.,Passport No.,National ID,Error Code,Error Detail{Environment.NewLine}" +
                             $"1,John Doe,EMP001,A123456,HK123456,INVALID_NAME,Name is required{Environment.NewLine}" +
                             $"3,Bob Wilson,EMP003,C345678,HK345678,INVALID_PASSPORT,Passport number format is invalid{Environment.NewLine}";

        result.Should().Be(expectedCsv);
    }

    [Fact]
    [Trait("Ticket", "CH-18824")]
    public void BuildValidationErrorDetails_WithMissingValues_ShouldHandleNullValues()
    {
        // Arrange
        var contents = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Name", null }, { "Staff No.", "EMP001" }, { "Passport No.", null }, { "National ID", "HK123456" } },
            new Dictionary<string, string?> { { "Name", "Jane Smith" }, { "Staff No.", null }, { "Passport No.", "B789012" }, { "National ID", null } }
        };

        var errors = new List<PolicyMemberUploadValidationError>
        {
            CreateValidationError(1, "MISSING_NAME", "Name is required"),
            CreateValidationError(2, "MISSING_STAFF_NO", "Staff number is required")
        };

        // Act
        string result = PolicyMemberUploadValidationErrorExporterService.BuildValidationErrorDetails(contents, errors);

        // Assert
        string expectedCsv = $"Row Index,Name,Staff No.,Passport No.,National ID,Error Code,Error Detail{Environment.NewLine}" +
                             $"1,,EMP001,,HK123456,MISSING_NAME,Name is required{Environment.NewLine}" +
                             $"2,Jane Smith,,B789012,,MISSING_STAFF_NO,Staff number is required{Environment.NewLine}";

        result.Should().Be(expectedCsv);
    }

    [Fact]
    [Trait("Ticket", "CH-18824")]
    public void BuildValidationErrorDetails_WithErrorMessagesContainingCommas_ShouldRemoveCommas()
    {
        // Arrange
        var contents = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Name", "John Doe" }, { "Staff No.", "EMP001" }, { "Passport No.", "A123456" }, { "National ID", "HK123456" } }
        };

        var errors = new List<PolicyMemberUploadValidationError>
        {
            CreateValidationError(1, "INVALID_FORMAT", "Invalid format, please check the data, and try again")
        };

        // Act
        string result = PolicyMemberUploadValidationErrorExporterService.BuildValidationErrorDetails(contents, errors);

        // Assert
        string expectedCsv = $"Row Index,Name,Staff No.,Passport No.,National ID,Error Code,Error Detail{Environment.NewLine}" +
                             $"1,John Doe,EMP001,A123456,HK123456,INVALID_FORMAT,Invalid format please check the data and try again{Environment.NewLine}";

        result.Should().Be(expectedCsv);
    }

    [Fact]
    [Trait("Ticket", "CH-18824")]
    public void BuildValidationErrorDetails_WithErrorsNotInOrder_ShouldSortByRowIndex()
    {
        // Arrange
        var contents = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Name", "John Doe" }, { "Staff No.", "EMP001" }, { "Passport No.", "A123456" }, { "National ID", "HK123456" } },
            new Dictionary<string, string?> { { "Name", "Jane Smith" }, { "Staff No.", "EMP002" }, { "Passport No.", "B789012" }, { "National ID", "HK789012" } },
            new Dictionary<string, string?> { { "Name", "Bob Wilson" }, { "Staff No.", "EMP003" }, { "Passport No.", "C345678" }, { "National ID", "HK345678" } }
        };

        var errors = new List<PolicyMemberUploadValidationError>
        {
            CreateValidationError(3, "INVALID_PASSPORT", "Passport number format is invalid"),
            CreateValidationError(1, "INVALID_NAME", "Name is required"),
            CreateValidationError(2, "INVALID_STAFF_NO", "Staff number format is invalid")
        };

        // Act
        string result = PolicyMemberUploadValidationErrorExporterService.BuildValidationErrorDetails(contents, errors);

        // Assert
        string expectedCsv = $"Row Index,Name,Staff No.,Passport No.,National ID,Error Code,Error Detail{Environment.NewLine}" +
                             $"1,John Doe,EMP001,A123456,HK123456,INVALID_NAME,Name is required{Environment.NewLine}" +
                             $"2,Jane Smith,EMP002,B789012,HK789012,INVALID_STAFF_NO,Staff number format is invalid{Environment.NewLine}" +
                             $"3,Bob Wilson,EMP003,C345678,HK345678,INVALID_PASSPORT,Passport number format is invalid{Environment.NewLine}";

        result.Should().Be(expectedCsv);
    }

    [Fact]
    [Trait("Ticket", "CH-18824")]
    public void BuildValidationErrorDetails_WithEmptyErrorsList_ShouldReturnOnlyHeader()
    {
        // Arrange
        var contents = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Name", "John Doe" }, { "Staff No.", "EMP001" }, { "Passport No.", "A123456" }, { "National ID", "HK123456" } }
        };

        var errors = new List<PolicyMemberUploadValidationError>();

        // Act
        string result = PolicyMemberUploadValidationErrorExporterService.BuildValidationErrorDetails(contents, errors);

        // Assert
        string expectedCsv = $"Row Index,Name,Staff No.,Passport No.,National ID,Error Code,Error Detail{Environment.NewLine}";
        result.Should().Be(expectedCsv);
    }

    [Fact]
    [Trait("Ticket", "CH-18824")]
    public void BuildValidationErrorDetails_WithMissingFieldsInContent_ShouldUseEmptyStrings()
    {
        // Arrange
        var contents = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Name", "John Doe" } } // Missing other fields
        };

        var errors = new List<PolicyMemberUploadValidationError>
        {
            CreateValidationError(1, "MISSING_FIELDS", "Required fields are missing")
        };

        // Act
        string result = PolicyMemberUploadValidationErrorExporterService.BuildValidationErrorDetails(contents, errors);

        // Assert
        string expectedCsv = $"Row Index,Name,Staff No.,Passport No.,National ID,Error Code,Error Detail{Environment.NewLine}" +
                             $"1,John Doe,,,,MISSING_FIELDS,Required fields are missing{Environment.NewLine}";

        result.Should().Be(expectedCsv);
    }

    [Fact]
    [Trait("Ticket", "CH-18824")]
    public void BuildValidationErrorDetails_WithOutOfBoundsRowIndex_ShouldOutputPlaceholder()
    {
        // Arrange
        var contents = new List<IReadOnlyDictionary<string, string?>>
        {
            new Dictionary<string, string?> { { "Name", "John Doe" }, { "Staff No.", "EMP001" }, { "Passport No.", "A123456" }, { "National ID", "HK123456" } }
        };
        var errors = new List<PolicyMemberUploadValidationError>
        {
            CreateValidationError(0, "ERR_CODE", "Error message for row 0"), // 0 is out of bounds
            CreateValidationError(2, "ERR_CODE2", "Error message for row 2") // 2 is out of bounds (contents.Count == 1)
        };

        // Act
        string result = PolicyMemberUploadValidationErrorExporterService.BuildValidationErrorDetails(contents, errors);

        // Assert
        string expectedCsv = $"Row Index,Name,Staff No.,Passport No.,National ID,Error Code,Error Detail{Environment.NewLine}" +
                             $"0,,,,,ERR_CODE,Error message for row 0{Environment.NewLine}" +
                             $"2,,,,,ERR_CODE2,Error message for row 2{Environment.NewLine}";
        result.Should().Be(expectedCsv);
    }

    #endregion

    #region PersistValidationErrorsAsync Tests

    [Fact]
    [Trait("Category", "Unit")]
    public async Task PersistValidationErrorsAsync_WithValidErrors_ShouldCallInsertBatchAsync()
    {
        // Arrange
        var mockFileParserFactory = new Mock<IFileParserFactory>();
        var mockFileSystemService = new Mock<IFileSystemService>();
        var mockUploadRepository = new Mock<IPaginatedRepository<PolicyMemberUpload, PolicyMemberUploadId>>();
        var mockValidationErrorRepository = new Mock<IPaginatedRepository<PolicyMemberUploadValidationError, PolicyMemberUploadValidationErrorId>>();
        var mockDbContext = new Mock<ApplicationDbContext>();
        var mockLogger = new Mock<ILogger<PolicyMemberUploadValidationErrorExporterService>>();

        var service = new PolicyMemberUploadValidationErrorExporterService(
            mockFileParserFactory.Object,
            mockFileSystemService.Object,
            mockUploadRepository.Object,
            mockValidationErrorRepository.Object,
            mockDbContext.Object,
            mockLogger.Object);

        var validationErrors = new List<PolicyMemberUploadValidationError>
        {
            CreateValidationError(1, "INVALID_NAME", "Name is required"),
            CreateValidationError(2, "INVALID_EMAIL", "Email format is invalid")
        };

        // Act
        await service.PersistValidationErrorsAsync(validationErrors, CancellationToken.None);

        // Assert
        mockValidationErrorRepository.Verify(
            x => x.InsertBatchAsync(It.Is<List<PolicyMemberUploadValidationError>>(list => list.Count == 2), CancellationToken.None),
            Times.Once);
    }

    [Fact]
    [Trait("Category", "Unit")]
    public async Task PersistValidationErrorsAsync_WithEmptyCollection_ShouldNotCallRepository()
    {
        // Arrange
        var mockFileParserFactory = new Mock<IFileParserFactory>();
        var mockFileSystemService = new Mock<IFileSystemService>();
        var mockUploadRepository = new Mock<IPaginatedRepository<PolicyMemberUpload, PolicyMemberUploadId>>();
        var mockValidationErrorRepository = new Mock<IPaginatedRepository<PolicyMemberUploadValidationError, PolicyMemberUploadValidationErrorId>>();
        var mockLogger = new Mock<ILogger<PolicyMemberUploadValidationErrorExporterService>>();

        var mockDbContext = new Mock<ApplicationDbContext>();
        var service = new PolicyMemberUploadValidationErrorExporterService(
            mockFileParserFactory.Object,
            mockFileSystemService.Object,
            mockUploadRepository.Object,
            mockValidationErrorRepository.Object,
            mockDbContext.Object,
            mockLogger.Object);

        var validationErrors = new List<PolicyMemberUploadValidationError>();

        // Act
        await service.PersistValidationErrorsAsync(validationErrors, CancellationToken.None);

        // Assert
        mockValidationErrorRepository.Verify(
            x => x.InsertBatchAsync(It.IsAny<List<PolicyMemberUploadValidationError>>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    [Trait("Category", "Unit")]
    public async Task PersistValidationErrorsAsync_WithRepositoryException_ShouldLogErrorAndRethrow()
    {
        // Arrange
        var mockFileParserFactory = new Mock<IFileParserFactory>();
        var mockFileSystemService = new Mock<IFileSystemService>();
        var mockUploadRepository = new Mock<IPaginatedRepository<PolicyMemberUpload, PolicyMemberUploadId>>();
        var mockValidationErrorRepository = new Mock<IPaginatedRepository<PolicyMemberUploadValidationError, PolicyMemberUploadValidationErrorId>>();
        var mockLogger = new Mock<ILogger<PolicyMemberUploadValidationErrorExporterService>>();

        var expectedException = new InvalidOperationException("Database connection failed");
        mockValidationErrorRepository
            .Setup(x => x.InsertBatchAsync(It.IsAny<List<PolicyMemberUploadValidationError>>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        var mockDbContext = new Mock<ApplicationDbContext>();
        var service = new PolicyMemberUploadValidationErrorExporterService(
            mockFileParserFactory.Object,
            mockFileSystemService.Object,
            mockUploadRepository.Object,
            mockValidationErrorRepository.Object,
            mockDbContext.Object,
            mockLogger.Object);

        var validationErrors = new List<PolicyMemberUploadValidationError>
        {
            CreateValidationError(1, "INVALID_NAME", "Name is required")
        };

        // Act & Assert
        var thrownException = await Assert.ThrowsAsync<InvalidOperationException>(
            () => service.PersistValidationErrorsAsync(validationErrors, CancellationToken.None));

        Assert.Same(expectedException, thrownException);

        // Verify error logging
        mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Failed to persist 1 validation errors to database")),
                expectedException,
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    [Trait("Category", "Unit")]
    public async Task PersistValidationErrorsAsync_WithLargeCollection_ShouldHandleEfficiently()
    {
        // Arrange
        var mockFileParserFactory = new Mock<IFileParserFactory>();
        var mockFileSystemService = new Mock<IFileSystemService>();
        var mockUploadRepository = new Mock<IPaginatedRepository<PolicyMemberUpload, PolicyMemberUploadId>>();
        var mockValidationErrorRepository = new Mock<IPaginatedRepository<PolicyMemberUploadValidationError, PolicyMemberUploadValidationErrorId>>();
        var mockLogger = new Mock<ILogger<PolicyMemberUploadValidationErrorExporterService>>();

        var mockDbContext = new Mock<ApplicationDbContext>();
        var service = new PolicyMemberUploadValidationErrorExporterService(
            mockFileParserFactory.Object,
            mockFileSystemService.Object,
            mockUploadRepository.Object,
            mockValidationErrorRepository.Object,
            mockDbContext.Object,
            mockLogger.Object);

        // Create a large collection of validation errors
        var validationErrors = new List<PolicyMemberUploadValidationError>();
        for (int i = 1; i <= 1000; i++)
        {
            validationErrors.Add(CreateValidationError(i, $"ERROR_{i}", $"Error message {i}"));
        }

        // Act
        await service.PersistValidationErrorsAsync(validationErrors, CancellationToken.None);

        // Assert
        mockValidationErrorRepository.Verify(
            x => x.InsertBatchAsync(It.Is<List<PolicyMemberUploadValidationError>>(list => list.Count == 1000), CancellationToken.None),
            Times.Once);

        // Verify debug logging
        mockLogger.Verify(
            x => x.Log(
                LogLevel.Debug,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Successfully persisted 1000 validation errors to database")),
                null,
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    [Trait("Category", "Unit")]
    public async Task PersistValidationErrorsAsync_WithCancellationToken_ShouldPassTokenToRepository()
    {
        // Arrange
        var mockFileParserFactory = new Mock<IFileParserFactory>();
        var mockFileSystemService = new Mock<IFileSystemService>();
        var mockUploadRepository = new Mock<IPaginatedRepository<PolicyMemberUpload, PolicyMemberUploadId>>();
        var mockValidationErrorRepository = new Mock<IPaginatedRepository<PolicyMemberUploadValidationError, PolicyMemberUploadValidationErrorId>>();
        var mockLogger = new Mock<ILogger<PolicyMemberUploadValidationErrorExporterService>>();

        var mockDbContext = new Mock<ApplicationDbContext>();
        var service = new PolicyMemberUploadValidationErrorExporterService(
            mockFileParserFactory.Object,
            mockFileSystemService.Object,
            mockUploadRepository.Object,
            mockValidationErrorRepository.Object,
            mockDbContext.Object,
            mockLogger.Object);

        var validationErrors = new List<PolicyMemberUploadValidationError>
        {
            CreateValidationError(1, "INVALID_NAME", "Name is required")
        };

        var cancellationToken = new CancellationToken();

        // Act
        await service.PersistValidationErrorsAsync(validationErrors, cancellationToken);

        // Assert
        mockValidationErrorRepository.Verify(
            x => x.InsertBatchAsync(It.IsAny<List<PolicyMemberUploadValidationError>>(), cancellationToken),
            Times.Once);
    }

    [Fact]
    [Trait("Category", "Unit")]
    public async Task PersistValidationErrorsAsync_WithSingleError_ShouldPersistCorrectly()
    {
        // Arrange
        var mockFileParserFactory = new Mock<IFileParserFactory>();
        var mockFileSystemService = new Mock<IFileSystemService>();
        var mockUploadRepository = new Mock<IPaginatedRepository<PolicyMemberUpload, PolicyMemberUploadId>>();
        var mockValidationErrorRepository = new Mock<IPaginatedRepository<PolicyMemberUploadValidationError, PolicyMemberUploadValidationErrorId>>();
        var mockLogger = new Mock<ILogger<PolicyMemberUploadValidationErrorExporterService>>();

        var mockDbContext = new Mock<ApplicationDbContext>();
        var service = new PolicyMemberUploadValidationErrorExporterService(
            mockFileParserFactory.Object,
            mockFileSystemService.Object,
            mockUploadRepository.Object,
            mockValidationErrorRepository.Object,
            mockDbContext.Object,
            mockLogger.Object);

        var uploadId = PolicyMemberUploadId.New;
        var validationError = CreateValidationErrorWithUploadId(uploadId, 1, "INVALID_EMAIL", "Email format is invalid");
        var validationErrors = new List<PolicyMemberUploadValidationError> { validationError };

        // Act
        await service.PersistValidationErrorsAsync(validationErrors, CancellationToken.None);

        // Assert
        mockValidationErrorRepository.Verify(
            x => x.InsertBatchAsync(
                It.Is<List<PolicyMemberUploadValidationError>>(list =>
                    list.Count == 1 &&
                    list[0].RowIndex == 1 &&
                    list[0].Code == "INVALID_EMAIL" &&
                    list[0].Message == "Email format is invalid"),
                CancellationToken.None),
            Times.Once);
    }

    [Fact]
    [Trait("Category", "Unit")]
    public async Task PersistValidationErrorsAsync_WithMultipleErrorsSameRow_ShouldPersistAll()
    {
        // Arrange
        var mockFileParserFactory = new Mock<IFileParserFactory>();
        var mockFileSystemService = new Mock<IFileSystemService>();
        var mockUploadRepository = new Mock<IPaginatedRepository<PolicyMemberUpload, PolicyMemberUploadId>>();
        var mockValidationErrorRepository = new Mock<IPaginatedRepository<PolicyMemberUploadValidationError, PolicyMemberUploadValidationErrorId>>();
        var mockLogger = new Mock<ILogger<PolicyMemberUploadValidationErrorExporterService>>();

        var mockDbContext = new Mock<ApplicationDbContext>();
        var service = new PolicyMemberUploadValidationErrorExporterService(
            mockFileParserFactory.Object,
            mockFileSystemService.Object,
            mockUploadRepository.Object,
            mockValidationErrorRepository.Object,
            mockDbContext.Object,
            mockLogger.Object);

        var validationErrors = new List<PolicyMemberUploadValidationError>
        {
            CreateValidationError(1, "INVALID_NAME", "Name is required"),
            CreateValidationError(1, "INVALID_EMAIL", "Email format is invalid"),
            CreateValidationError(1, "INVALID_PHONE", "Phone number format is invalid")
        };

        // Act
        await service.PersistValidationErrorsAsync(validationErrors, CancellationToken.None);

        // Assert
        mockValidationErrorRepository.Verify(
            x => x.InsertBatchAsync(
                It.Is<List<PolicyMemberUploadValidationError>>(list =>
                    list.Count == 3 &&
                    list.All(e => e.RowIndex == 1)),
                CancellationToken.None),
            Times.Once);
    }

    [Fact]
    [Trait("Category", "Unit")]
    public async Task PersistValidationErrorsAsync_WithDifferentErrorTypes_ShouldPersistAll()
    {
        // Arrange
        var mockFileParserFactory = new Mock<IFileParserFactory>();
        var mockFileSystemService = new Mock<IFileSystemService>();
        var mockUploadRepository = new Mock<IPaginatedRepository<PolicyMemberUpload, PolicyMemberUploadId>>();
        var mockValidationErrorRepository = new Mock<IPaginatedRepository<PolicyMemberUploadValidationError, PolicyMemberUploadValidationErrorId>>();
        var mockLogger = new Mock<ILogger<PolicyMemberUploadValidationErrorExporterService>>();

        var mockDbContext = new Mock<ApplicationDbContext>();
        var service = new PolicyMemberUploadValidationErrorExporterService(
            mockFileParserFactory.Object,
            mockFileSystemService.Object,
            mockUploadRepository.Object,
            mockValidationErrorRepository.Object,
            mockDbContext.Object,
            mockLogger.Object);

        var validationErrors = new List<PolicyMemberUploadValidationError>
        {
            CreateValidationError(1, "REQUIRED_FIELD", "Name is required"),
            CreateValidationError(2, "INVALID_FORMAT", "Email format is invalid"),
            CreateValidationError(3, "DUPLICATE_VALUE", "Member ID already exists"),
            CreateValidationError(4, "INVALID_OPTION", "Invalid plan selection")
        };

        // Act
        await service.PersistValidationErrorsAsync(validationErrors, CancellationToken.None);

        // Assert
        mockValidationErrorRepository.Verify(
            x => x.InsertBatchAsync(
                It.Is<List<PolicyMemberUploadValidationError>>(list =>
                    list.Count == 4 &&
                    list.Any(e => e.Code == "REQUIRED_FIELD") &&
                    list.Any(e => e.Code == "INVALID_FORMAT") &&
                    list.Any(e => e.Code == "DUPLICATE_VALUE") &&
                    list.Any(e => e.Code == "INVALID_OPTION")),
                CancellationToken.None),
            Times.Once);
    }

    [Fact]
    [Trait("Category", "Integration")]
    public async Task PersistValidationErrorsAsync_IntegrationWithHandler_ShouldWorkCorrectly()
    {
        // Arrange
        var mockFileParserFactory = new Mock<IFileParserFactory>();
        var mockFileSystemService = new Mock<IFileSystemService>();
        var mockUploadRepository = new Mock<IPaginatedRepository<PolicyMemberUpload, PolicyMemberUploadId>>();
        var mockValidationErrorRepository = new Mock<IPaginatedRepository<PolicyMemberUploadValidationError, PolicyMemberUploadValidationErrorId>>();
        var mockLogger = new Mock<ILogger<PolicyMemberUploadValidationErrorExporterService>>();

        var mockDbContext = new Mock<ApplicationDbContext>();
        var service = new PolicyMemberUploadValidationErrorExporterService(
            mockFileParserFactory.Object,
            mockFileSystemService.Object,
            mockUploadRepository.Object,
            mockValidationErrorRepository.Object,
            mockDbContext.Object,
            mockLogger.Object);

        // Simulate validation errors that would come from a real validation scenario
        var uploadId = PolicyMemberUploadId.New;
        var validationErrors = new List<PolicyMemberUploadValidationError>
        {
            CreateValidationErrorWithUploadId(uploadId, 1, "REQUIRED_FIELD", "Name is required"),
            CreateValidationErrorWithUploadId(uploadId, 2, "INVALID_FORMAT", "Email format is invalid"),
            CreateValidationErrorWithUploadId(uploadId, 3, "DUPLICATE_VALUE", "Member ID already exists")
        };

        // Act
        await service.PersistValidationErrorsAsync(validationErrors, CancellationToken.None);

        // Assert - Verify the repository was called with the correct parameters
        mockValidationErrorRepository.Verify(
            x => x.InsertBatchAsync(
                It.Is<List<PolicyMemberUploadValidationError>>(list =>
                    list.Count == 3 &&
                    list.All(e => e.PolicyMemberUploadId == uploadId) &&
                    list[0].RowIndex == 1 && list[0].Code == "REQUIRED_FIELD" &&
                    list[1].RowIndex == 2 && list[1].Code == "INVALID_FORMAT" &&
                    list[2].RowIndex == 3 && list[2].Code == "DUPLICATE_VALUE"),
                CancellationToken.None),
            Times.Once);

        // Verify debug logging was called
        mockLogger.Verify(
            x => x.Log(
                LogLevel.Debug,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Successfully persisted 3 validation errors to database")),
                null,
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    #endregion

    #region Helper Methods

    private static PolicyMemberUploadValidationError CreateValidationError(int rowIndex, string code, string message)
    {
        return PolicyMemberUploadValidationError.Create(
            PolicyMemberUploadId.Empty, // Use empty ID for testing
            rowIndex,
            code,
            message);
    }

    private static PolicyMemberUploadValidationError CreateValidationErrorWithUploadId(PolicyMemberUploadId uploadId, int rowIndex, string code, string message)
    {
        return PolicyMemberUploadValidationError.Create(
            uploadId,
            rowIndex,
            code,
            message);
    }

    #endregion
}