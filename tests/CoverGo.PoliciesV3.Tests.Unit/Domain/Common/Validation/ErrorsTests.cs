using CoverGo.PoliciesV3.Domain.Common.Validation;

namespace CoverGo.PoliciesV3.Tests.Unit.Domain.Common.Validation;

/// <summary>
/// Unit tests for the Errors factory class to ensure correct validation error creation.
/// </summary>
public class ErrorsTests
{
    [Fact]
    public void Required_WithPropertyPathOnly_ShouldCreateRequiredError()
    {
        // Act
        ValidationError error = Errors.Required("email");

        // Assert
        error.Code.Should().Be(ErrorCodes.Required);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("email");
        error.Message.Should().Be("email is required");
    }

    [Fact]
    public void Required_WithPropertyPathAndLabel_ShouldCreateRequiredError()
    {
        // Act
        ValidationError error = Errors.Required("email", "Email Address");

        // Assert
        error.Code.Should().Be(ErrorCodes.Required);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email Address");
        error.Message.Should().Be("Email Address is required");
    }

    [Fact]
    public void InvalidFormat_ShouldCreateInvalidFormatError()
    {
        // Act
        ValidationError error = Errors.InvalidFormat("phone", "Phone Number");

        // Assert
        error.Code.Should().Be(ErrorCodes.InvalidFormat);
        error.PropertyPath.Should().Be("phone");
        error.PropertyLabel.Should().Be("Phone Number");
        error.Message.Should().Be("Phone Number has an invalid format");
    }

    [Fact]
    public void UniqueViolation_WithDefaultScope_ShouldCreateUniqueError()
    {
        // Act
        ValidationError error = Errors.UniqueViolation("username", "Username");

        // Assert
        error.Code.Should().Be(ErrorCodes.UniqueViolation);
        error.PropertyPath.Should().Be("username");
        error.PropertyLabel.Should().Be("Username");
        error.Context["Scope"].Should().Be("upload");
        error.Message.Should().Be("Username must be unique within upload");
    }

    [Fact]
    public void UniqueViolation_WithCustomScope_ShouldCreateUniqueError()
    {
        // Act
        ValidationError error = Errors.UniqueViolation("email", "Email", "database");

        // Assert
        error.Code.Should().Be(ErrorCodes.UniqueViolation);
        error.PropertyPath.Should().Be("email");
        error.PropertyLabel.Should().Be("Email");
        error.Context["Scope"].Should().Be("database");
        error.Message.Should().Be("Email must be unique within database");
    }

    [Fact]
    public void InvalidOption_ShouldCreateInvalidOptionError()
    {
        // Arrange
        string[] availableOptions = ["active", "inactive", "pending"];

        // Act
        ValidationError error = Errors.InvalidOption("status", availableOptions, "Status");

        // Assert
        error.Code.Should().Be(ErrorCodes.InvalidOption);
        error.PropertyPath.Should().Be("status");
        error.PropertyLabel.Should().Be("Status");
        error.Context["AvailableOptions"].Should().BeEquivalentTo(availableOptions);
    }

    [Fact]
    public void NotAllowed_ShouldCreateNotAllowedError()
    {
        // Act
        ValidationError error = Errors.NotAllowed("formula", "Formula Field");

        // Assert
        error.Code.Should().Be(ErrorCodes.NotAllowed);
        error.PropertyPath.Should().Be("formula");
        error.PropertyLabel.Should().Be("Formula Field");
    }

    [Fact]
    public void ChildMinDays_ShouldCreateChildMinDaysError()
    {
        // Act
        ValidationError error = Errors.ChildMinDays("birthDate", 30, 15, "Birth Date");

        // Assert
        error.Code.Should().Be(ErrorCodes.ChildMinDays);
        error.PropertyPath.Should().Be("birthDate");
        error.PropertyLabel.Should().Be("Birth Date");
        error.Context["MinDays"].Should().Be(30);
        error.Context["ActualDays"].Should().Be(15);
    }

    [Fact]
    public void SpouseMinAge_ShouldCreateSpouseMinAgeError()
    {
        // Act
        ValidationError error = Errors.SpouseMinAge("birthDate", 18, 16, "Birth Date");

        // Assert
        error.Code.Should().Be(ErrorCodes.SpouseMinAge);
        error.PropertyPath.Should().Be("birthDate");
        error.PropertyLabel.Should().Be("Birth Date");
        error.Context["MinAge"].Should().Be(18);
        error.Context["ActualAge"].Should().Be(16);
    }

    [Fact]
    public void EmployeeMinAge_ShouldCreateEmployeeMinAgeError()
    {
        // Act
        ValidationError error = Errors.EmployeeMinAge("birthDate", 18, 16, "Birth Date");

        // Assert
        error.Code.Should().Be(ErrorCodes.EmployeeMinAge);
        error.PropertyPath.Should().Be("birthDate");
        error.PropertyLabel.Should().Be("Birth Date");
        error.Context["MinAge"].Should().Be(18);
        error.Context["ActualAge"].Should().Be(16);
    }

    [Fact]
    public void EmployeeMaxAge_ShouldCreateEmployeeMaxAgeError()
    {
        // Act
        ValidationError error = Errors.EmployeeMaxAge("birthDate", 65, 70, "Birth Date");

        // Assert
        error.Code.Should().Be(ErrorCodes.EmployeeMaxAge);
        error.PropertyPath.Should().Be("birthDate");
        error.PropertyLabel.Should().Be("Birth Date");
        error.Context["MaxAge"].Should().Be(65);
        error.Context["ActualAge"].Should().Be(70);
    }

    [Fact]
    public void MemberNotFound_ShouldCreateMemberNotFoundError()
    {
        // Act
        ValidationError error = Errors.MemberNotFound("dependentOf", "EMP001", "Dependent Of");

        // Assert
        error.Code.Should().Be(ErrorCodes.MemberNotFound);
        error.PropertyPath.Should().Be("dependentOf");
        error.PropertyLabel.Should().Be("Dependent Of");
        error.Context["MemberId"].Should().Be("EMP001");
    }

    [Fact]
    public void MemberIdTaken_ShouldCreateMemberIdTakenError()
    {
        // Act
        ValidationError error = Errors.MemberIdTaken("memberId", "EMP001", "existing-id", "Member ID");

        // Assert
        error.Code.Should().Be(ErrorCodes.MemberIdTaken);
        error.PropertyPath.Should().Be("memberId");
        error.PropertyLabel.Should().Be("Member ID");
        error.Context["MemberId"].Should().Be("EMP001");
        error.Context["ExistingPolicyMemberId"].Should().Be("existing-id");
    }

    [Fact]
    public void MemberNotInContractHolder_ShouldCreateMemberNotInContractHolderError()
    {
        // Act
        ValidationError error = Errors.MemberNotInContractHolder("memberId", "EMP001", "POL001", "Member ID");

        // Assert
        error.Code.Should().Be(ErrorCodes.MemberNotInContractHolder);
        error.PropertyPath.Should().Be("memberId");
        error.PropertyLabel.Should().Be("Member ID");
        error.Context["MemberId"].Should().Be("EMP001");
        error.Context["PolicyId"].Should().Be("POL001");
    }

    [Fact]
    public void InvalidPlanId_ShouldCreateInvalidPlanIdError()
    {
        // Arrange
        string[] availablePlans = ["PLAN_A", "PLAN_B", "PLAN_C"];

        // Act
        ValidationError error = Errors.InvalidPlanId("planId", "INVALID_PLAN", availablePlans, "Plan ID", "test-product");

        // Assert
        error.Code.Should().Be(ErrorCodes.InvalidPlanId);
        error.PropertyPath.Should().Be("planId");
        error.PropertyLabel.Should().Be("Plan ID");
        error.Context["PlanId"].Should().Be("INVALID_PLAN");
        error.Context["AvailablePlans"].Should().BeEquivalentTo(availablePlans);
        error.Context["ProductId"].Should().Be("test-product");
    }

    [Fact]
    public void DependentPlanMismatch_ShouldCreateDependentPlanMismatchError()
    {
        // Act
        ValidationError error = Errors.DependentPlanMismatch("planId", "Plan ID");

        // Assert
        error.Code.Should().Be(ErrorCodes.DependentPlanMismatch);
        error.PropertyPath.Should().Be("planId");
        error.PropertyLabel.Should().Be("Plan ID");
    }

    [Fact]
    public void EmptyFile_ShouldCreateEmptyFileError()
    {
        // Act
        ValidationError error = Errors.EmptyFile();

        // Assert
        error.Code.Should().Be(ErrorCodes.EmptyFile);
        error.PropertyPath.Should().Be("file");
        error.PropertyLabel.Should().Be("Upload File");
    }

    [Fact]
    public void MissingColumns_ShouldCreateMissingColumnsError()
    {
        // Arrange
        string[] missingColumns = ["email", "phone", "address"];

        // Act
        ValidationError error = Errors.MissingColumns(missingColumns);

        // Assert
        error.Code.Should().Be(ErrorCodes.MissingColumns);
        error.PropertyPath.Should().Be("columns");
        error.PropertyLabel.Should().Be("Columns");
        error.Context["MissingColumns"].Should().BeEquivalentTo(missingColumns);
    }

    [Fact]
    public void ExtraColumns_ShouldCreateExtraColumnsError()
    {
        // Arrange
        string[] extraColumns = ["unknown1", "unknown2"];

        // Act
        ValidationError error = Errors.ExtraColumns(extraColumns);

        // Assert
        error.Code.Should().Be(ErrorCodes.ExtraColumns);
        error.PropertyPath.Should().Be("columns");
        error.PropertyLabel.Should().Be("Columns");
        error.Context["ExtraColumns"].Should().BeEquivalentTo(extraColumns);
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    public void Required_WithInvalidPropertyPath_ShouldThrowArgumentException(string? propertyPath)
    {
        // Act & Assert
        Func<ValidationError> act = () => Errors.Required(propertyPath!);
        act.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void UnexpectedError_WithErrorMessage_ShouldCreateValidationErrorWithCorrectProperties()
    {
        // Arrange
        const string errorMessage = "Something unexpected happened";

        // Act
        ValidationError result = Errors.UnexpectedError(errorMessage);

        // Assert
        result.Should().NotBeNull();
        result.Code.Should().Be(ErrorCodes.UnexpectedError);
        result.PropertyPath.Should().Be("operation");
        result.PropertyLabel.Should().Be("Operation");
        result.Context.Should().ContainKey("ErrorMessage");
        result.Context["ErrorMessage"].Should().Be(errorMessage);
    }

    [Fact]
    public void UnexpectedError_WithEmptyMessage_ShouldCreateValidationErrorWithEmptyContext()
    {
        // Arrange
        const string errorMessage = "";

        // Act
        ValidationError result = Errors.UnexpectedError(errorMessage);

        // Assert
        result.Should().NotBeNull();
        result.Code.Should().Be(ErrorCodes.UnexpectedError);
        result.PropertyPath.Should().Be("operation");
        result.PropertyLabel.Should().Be("Operation");
        result.Context.Should().ContainKey("ErrorMessage");
        result.Context["ErrorMessage"].Should().Be(errorMessage);
    }

    [Theory]
    [InlineData("Database connection failed")]
    [InlineData("Network timeout occurred")]
    [InlineData("Invalid operation state")]
    [InlineData("Cancellation failed: Upload not found")]
    public void UnexpectedError_WithVariousErrorMessages_ShouldCreateValidationErrorWithCorrectMessage(string errorMessage)
    {
        // Act
        ValidationError result = Errors.UnexpectedError(errorMessage);

        // Assert
        result.Should().NotBeNull();
        result.Code.Should().Be(ErrorCodes.UnexpectedError);
        result.PropertyPath.Should().Be("operation");
        result.PropertyLabel.Should().Be("Operation");
        result.Context["ErrorMessage"].Should().Be(errorMessage);
    }
}